<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use App\Http\Models\Lesson;
use DateTime;

class LessonGroup extends Model{

    protected $table = 'lesson_group';

    //lấy ra các bài học trong group
    public function lessons(){
        return $this->hasMany('App\Http\Models\Lesson', 'group_id')->orderBy('sort_order')->where('show', '<>', '0');
    }

    public function lesson_default(){
        return $this->hasMany('App\Models\Backend\Lesson', 'group_id')->where('show', '<>', '0');
    }

    public function lessons_stat() {
      return $this->hasMany(LessonStat::class, 'group_id');
    }
    //lấy ra thông tin khóa học cha
    public function getCourseOfGroup(){
    	return $this->belongsTo('App\Http\Models\Course', 'course_id');
    }

    public function category() {
        return $this->belongsTo(LessonCategory::class, 'lesson_category_id');
    }
}
