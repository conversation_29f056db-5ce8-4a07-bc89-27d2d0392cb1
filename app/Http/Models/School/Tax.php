<?php

namespace App\Http\Models\School;

use App\Base\Model\BaseModel;
use App\Http\Models\Community\CommunityGroup;
use App\Http\Models\Course;
use App\Http\Models\Users;
use Illuminate\Support\Facades\DB;

class Tax extends BaseModel
{
    protected $connection = 'mysql3';
    protected $table = 'taxes';
    protected $table_name = 'taxes';
    protected $guard = [''];

    public function __construct() {
      $this->table = DB::connection($this->connection)->getDatabaseName() . '.' . $this->table_name;
    }
}
