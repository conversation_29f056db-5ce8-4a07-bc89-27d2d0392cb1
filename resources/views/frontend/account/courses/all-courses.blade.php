@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON><PERSON><PERSON> cá nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
@stop

@section('content')

    <div id="all_course" v-cloak>
        <div class="main">
            <div class="main-center">
                <div class="main-user-left">

                    {{-- import left menu --}}
                    @include('frontend.account._left')

                </div>

                <div class="main-user-right">

                    <div class="dm-tab">
                        <div class="tab-item active"><i class="zmdi zmdi-layers"></i> Khóa học đã mua</div>
                        {{-- <a href="{{url('/account/courses')}}/journey" ><div class="tab-item"><i class="zmdi zmdi-calendar-note"></i> Quá trình học tập</div></a> --}}
                        <a href="{{url('/account/courses')}}/test" ><div class="tab-item"><i class="zmdi zmdi-check-square"></i> Kết quả kiểm tra</div></a>
                    </div>
                    <div class="tab-content-container bought-courses">
                        <div class="bought-courses-container">
                            @foreach($listCourses as $course)

            {{-- @if($course->course_id != 31) --}}
                <div class="course-item">
                    <div class="images">
                        <a href="{{url('/khoa-hoc')}}/{{$course->course_id}}">
                            <img src="{{ url('cdn/course/small') }}/{{$course->banner}}">
                        </a>
                    </div>
                    <div class="info">
                        <div class="title">
                            <a href="{{url('/khoa-hoc')}}/{{$course->course_id}}">
                                <i class="fa fa-mortar-board"></i>  {{ $course->title }}
                                @if($course->extra_days > 0)<sup> <span class="label-hot">Plus</span></sup>@endif
                            </a>
                        </div>
                        <div class="name-gv" title="Giảng viên">Thầy Dũng</div>
                    </div>

                                        <?php $now = new DateTime(); $today = $now->getTimestamp(); ?>

                                    @if($today > strtotime($course->watch_expired_day) )
                                        <div class="expried" style="color: #e74c3c;"><i class="zmdi zmdi-alert-triangle"></i> Đã hết hạn {{ date("d/m/Y", strtotime($course->watch_expired_day)) }}</div>
                                    @else
                                        <div class="expried">Ngày hết hạn : {{ date("d/m/Y", strtotime($course->watch_expired_day)) }}</div>
                                    @endif
                                </div>
                                {{-- @elseif($course->course_id == 31)
                                    <div class="course-item">
                                        <div class="images">
                                            <a href="#"><img src="https://vn.dungmori.com/thumbld.png"></a>
                                        </div>
                                        <div class="info">
                                            <div class="title">
                                                <a href="#"><i class="fa fa-mortar-board"></i>  {{ $course->title }}</a>
                                            </div>
                                            <div class="name-gv" title="Giảng viên">Thầy Dũng</div>
                                        </div>
                                        <div class="expried"><b>Mở vào ngày 07/06/2021</b></div>
                                    </div>
                                @endif --}}
                            @endforeach

                            {{-- Nếu danh sách trống --}}
                            @if(sizeof($listCourses) == 0 )
                                <div class="notification-empty-container">
                                    <i class="fa fa-mortar-board"></i> Bạn chưa mua khóa học nào
                                </div>
                            @endif

                        </div>
                    </div>
                    <div class="dm-tab" style="margin-top: 0px;">
                        <div class="tab-item review-more-courses">Xem thêm các khóa học khác</div>
                    </div>
                    <div class="tab-content-container bought-other-courses">
                        <div class="bought-courses-container">
                            @foreach($listSuggests as $item)
                                <div class="course-item">
                                    <div class="images">
                                        <a href="{{url('/khoa-hoc')}}/{{$item->id}}"><img src="{{ url('cdn/course/small') }}/{{$item->avatar_name}}"></a>
                                    </div>
                                    <div class="info">
                                        <div class="title">
                                            <a href="{{url('/khoa-hoc')}}/{{$item->id}}">{{ $item->name }}</a>
                                        </div>
                                        <div class="name-gv" title="Giảng viên"> Thầy Dũng</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                </div>

            </div>
        </div>

        <div class="relative z-[100]">
            <!-- Background Overlay -->
            <div
                    class="fixed inset-0 transition-opacity"
                    v-if="isOpenPopupCongratulate"
                    @click="isOpenPopupCongratulate = false"
            ></div>

            <!-- Popup -->
            <div
                    class="fixed bottom-0 inset-x-0 rounded-t-lg transform transition-transform duration-300 px-[20px] py-[170px] md:p-[60px] text-center"
                    :class="{ 'translate-y-0': isOpenPopupCongratulate, 'translate-y-full': !isOpenPopupCongratulate }"
            >
                <div
                        class="px-[44px] py-[24px] bg-[#F0FFF1] max-w-[1096px] flex items-center justify-between mx-auto rounded-[24px]"
                        style="box-shadow: 0 0 24px 0 rgba(7, 64, 63, 0.4);"
                >
                    <div class="svg-icon">
                        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="20" fill="#57D061"/>
                            <path d="M27.6192 14.2861L17.143 24.7623L12.3811 20.0004" stroke="#EBFFEE" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="text-content">
                        <div class="text-[#57D061] text-[20px] font-beanbag leading-[16px] mb-[14px]">Kích hoạt thành công 🎊</div>
                        <div class="text-[#07403F] text-[20px] font-averta-regular font-[600] leading-[16px]">Khóa N5-N4 sơ cấp mới!</div>
                    </div>
                    <a
                            class="text-[#57D061] font-beanbag text-[20px] hover:underline underline hover:text-[#57D061]"
                            href="/khoa-hoc/so-cap-n5"
{{--                            @click="isOpenPopupCongratulate = false"--}}
                    >
                        Xem ngay
                    </a>
                </div>
            </div>
        </div>

    </div>

<script>
  $(".item-user").removeClass("current");
  $(".courses").addClass("current");
</script>
@stop

@section('footer-js')
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script type="text/javascript">
        new Vue({
            el: "#all_course",
            data: {
                isOpenPopupCongratulate: false
            },
            methods: [],
            mounted() {
                if (localStorage.getItem('congratulate_code_try_lesson') === '1') {
                    this.isOpenPopupCongratulate = true
                    localStorage.removeItem('congratulate_code_try_lesson')
                }
            }
        })
    </script>
@stop
