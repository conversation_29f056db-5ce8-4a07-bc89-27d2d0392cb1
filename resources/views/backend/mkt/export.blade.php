<table>
    <thead>
    <tr>
        <td>Mã</td>
        <td>Họ và tên</td>
        <td>Email</td>
        <td>SĐT</td>
        <td>Tr<PERSON>ng thái</td>
        <td>Lớp</td>
        <td><PERSON><PERSON><PERSON> cuối</td>
        <td>Tiến trình</td>
        <td>Th<PERSON><PERSON> gian h<PERSON></td>
        @if(in_array($courseId, [39,40]))
          @php
            $index = 0;
          @endphp
            @if ($type == 'day')
              @foreach($days as $key => $value)
                @if (is_string($key))
                  @php $index++ @endphp
                  <td>Ngày {{ $index }}</td>
                @endif
              @endforeach
            @else
              @foreach($weeks as $key => $value)
                <td>Tuần {{ array_search($key, array_keys($weeks)) + 1 }}</td>
              @endforeach
            @endif

        @else
            @foreach($weeks as $key => $value)
                <td><PERSON><PERSON><PERSON> {{ array_search($key, array_keys($weeks)) + 1 }} ({{ \Carbon\Carbon::parse($key)->format('d/m') }} - {{ \Carbon\Carbon::parse($key)->endOfWeek()->format('d/m') }})</td>
            @endforeach
        @endif

    </tr>
    </thead>
    <tbody>
    @foreach ($users as $key => $user)
        <tr>
            <td>{{ $key + 1 }}</td>
            <td>{{ $user['name'] }}</td>
            <td>{{ $user['email'] }}</td>
            <td>{{ $user['phone'] }}</td>
            <td>
                @if (!$user['fingerprint'] && !$user['platform'])
                    <b>Chưa đăng nhập</b>
                @elseif (!isset($user['progress']) || !count($user['progress']))
                    <b>Chưa học</b>
                @else
                    Đang học
                @endif
            </td>
            <td>
                {{ $user['group']['name'] ?? '' }}
            </td>
            <td>
              Buổi {{ $user['last_lesson'] ? ($user['last_lesson']['checkpoint'] ? $user['last_lesson']['checkpoint']['key'] : '--') : '--' }}
            </td>
            <td>{{ isset($user['course_progress']) ? round((int) $user['course_progress'] / $lessonCount, 2) : 0 }}%</td>
            <td>
                {{ isset($user['total']) ? floor(((int) $user['total'] / 1000) / 3600) . gmdate(":i:s", ((int) $user['total'] / 1000) % 3600): '00:00:00' }}
            </td>

            @if(in_array($courseId, [39,40]))
                @if ($type == 'day')
                    @foreach($days as $idx => $value)
                      @if(is_string($idx))
                        <td>
                          @if (!$exportAll)
                            {{ $user[$idx] }} / {{ $value }}
                          @endif
                          ({{ round(($user[$idx] / $value) * 100, 1) }}%)
                        </td>
                      @endif
                    @endforeach
                @else
                    @foreach($weeks as $idx => $value)
                      <td>
                        @if (!$exportAll)
                          {{ $user['w' .$idx] }} / {{ $value }}
                        @endif
                        ({{ round(($user['w' .$idx] / $value) * 100, 1) }}%)
                      </td>
                    @endforeach
                @endif
            @else
                @foreach($weeks as $idx => $value)
                    <td>
                        @if (!$exportAll)
                            {{ $user[$idx] }} / {{ $value }}
                        @endif
                        ({{ round(($user[$idx] / $value) * 100, 1) }}%)
                    </td>
                @endforeach
            @endif

        </tr>
    @endforeach
    </tbody>
</table>
