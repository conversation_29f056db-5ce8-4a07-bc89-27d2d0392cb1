<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Models\Community\CommunityExam;
use App\Http\Models\Community\CommunityGroup;
use App\Http\Models\Community\CommunityGroupUser;
use App\Http\Models\ThiThu\Question;
use App\Http\ModelsFrontend\ThiThu\Answer;
use App\Http\ModelsFrontend\ThiThu\Exam;
use App\Http\ModelsFrontend\ThiThu\Result;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Benchmark;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;

class AdTestController extends Controller
{

    public function index()
    {
        $auth = Auth::check();
        if (!$auth) {
            return view('errors.auth_required');
        }
        return view('frontend.ad_test.index');
    }

    public function getScheduleList(Request $request)
    {
        $userId = $request->get('userId');
        $availableCourse = Auth::user()->available_courses->pluck('title')->map(function ($title) {
          return substr($title, -2);
        });
        $data = Exam::query()
          ->with(['result' => function ($result) use ($userId) {
            $result->where('user_id', $userId);
          }])
          ->whereIn('course', $availableCourse)
          ->where('type', 3)
          ->where('time_start', '<=', now())
          ->where('time_end', '>=',  now())
          ->orderBy('course')
          ->get();
        return response()->json([
            'code' => 200,
            'data' => $data,
        ]);
    }

    public function getGroupList(Request $request)
    {
        $userId = $request->get('userId');
        $date = Carbon::parse($request->get('date'));
        $data = CommunityGroup::query()
            ->with(['exams' => function ($exam) use ($date) {
                $exam->with('exam')
                    ->whereDate('time_start', '<=', $date->format('Y-m-d'))
                    ->whereDate('time_end', '>=', $date->format('Y-m-d'))
                    ->get();
            }])
            ->whereHas('users', function ($user) use ($userId) {
                $user->where('user_id', $userId);
            })
            ->get()->makeHidden(['logs', 'links']);
        return response()->json([
            'code' => 200,
            'data' => $data,
        ]);
    }

    public function getExamList(Request $request)
    {
      $user = auth()->user();
      $availableCourse = $user->available_courses->pluck('title')->map(function ($title) {
        return substr($title, -2);
      });
      $levels = array_values(array_unique(array_intersect($availableCourse->toArray(), ['N1', 'N2', 'N3', 'N4', 'N5'])));
      $date = Carbon::parse($request->get('date'));
      $data = Exam::query()->where('type', 3)->whereDate('time_start', '<=', $date->format('Y-m-d'))
        ->whereDate('time_end', '>=', $date->format('Y-m-d'))->get();
      return response()->json([
        'code' => 200,
        'data' => $data,
        'levels' => $levels
      ]);
    }

    public function getExam(Request $request)
    {
        if (!Auth::check()) {
            abort(404);
        }
        $id = Crypt::decrypt($request->token);
        $exam = Exam::query()->with('lessons.questions.answers')->find($id);
        //        dd($exam->time_start);
        //        dd($exam->time_end);
        $result = Result::query()
            ->where('exam_id', $exam->id)
            ->where('user_id', Auth::user()->id)
            ->where('submit_at', '>=', $exam->time_start)
            ->where('submit_at', '<=', $exam->time_end)
            ->get();
        if (count($result) > 0) {
            return view('errors.ad_test_existed')->with('result', $result[0]);
        }
        if (Carbon::parse($exam->time_start) > Carbon::now() || Carbon::parse($exam->time_end) < Carbon::now()) {
            return view('errors.no_exam');
        }
        $data = [
            'exam_id' => $exam->id,
            'course' => $exam->course,
            'user_id' => Auth::user()->id,
            'platform' => 'web',
            'is_test' => 1
        ];
        $result = Result::query()
            ->where('exam_id', $exam->id)
            ->where('user_id', Auth::user()->id)
            ->where('is_test', 1)
            ->where(function ($q) use ($exam) {
                $q->where(function ($query) use ($exam) {
                    $query->where('submit_at', '>=', $exam->time_start)
                        ->where('submit_at', '<=', $exam->time_end);
                })
                    ->orWhereNull('submit_at');
            })
            ->first();
        if (!$result) {
          $result = Result::query()->create($data);
        }
        $autoSubmitTime = '';
        $timeEnd = Carbon::parse($exam->time_end);
        $resultCreatedAt = Carbon::parse($result->created_at);

        if($timeEnd->diffInMinutes($resultCreatedAt) < $exam->maximum_time) {
          $autoSubmitTime = $timeEnd->diffInMinutes($resultCreatedAt);
        } else {
          $autoSubmitTime = $exam->maximum_time;
        }
        return view('frontend.ad_test.exam')
            ->with('autoSubmitTime', $autoSubmitTime)
            ->with('exam', $exam);
    }

    public function submit(Request $request)
    {
        $examId = $request->examId;
        $exam = Exam::query()->with(['lessons' => function ($lesson) {
            $lesson->where('status', 1)->get();
        }])->find($examId);
        $answer1 = !$request->answer1 ? json_decode('{}') : array_unique($request->answer1);
        foreach ($answer1 as $key => $value) {
            $answer1[$key] = (int) $value;
        }
        $result = Result::query()
            ->where('exam_id', $examId)
            ->where('user_id', Auth::user()->id)
            ->where('is_test', 1)
            ->where(function ($q) use ($exam) {
                $q->where(function ($query) use ($exam) {
                    $query->where('submit_at', '>=', $exam->time_start)
                        ->where('submit_at', '<=', $exam->time_end);
                })
                    ->orWhereNull('submit_at');
            })
            ->first();
        $point1 = 0;
        foreach ($answer1 as $key => $value) {
            $question = Question::query()->with('lesson.exams')->find($key);
            if ($question->lesson->exams->first()->id == $examId) {
                $answer = Answer::query()->find($value);
                if ($answer->question->id == $question->id && $answer->is_true) {
                    $point1 += $question->point;
                }
            }
        }
        $point2 = 0;
        $point3 = 0;
        $result->data_1 = json_encode($answer1);
        $result->data_2 = '{}';
        $result->data_3 = '{}';
        $result->score_1 = $point1;
        $result->score_2 = $point2;
        $result->score_3 = $point3;
        $result['total_score'] = $result['score_1'] + $result['score_2'] + $result['score_3'];
        $result['is_passed'] = 1;
        if ($result['total_score'] < $exam->passed_point) {
            $result['is_passed'] = 0;
        }
        $intersect = array_intersect($exam->lessons->pluck('type')->toArray(), [1, 2, 3]);
        if (!in_array("1", $intersect)) {
            $result['data_1'] = json_encode($answer1);
            $result['score_1'] = 0;
        }
        $result['community_exam_id'] = null;
        $result['submit_at'] = Carbon::now();
        $result->save();
        return response()->json([
            'code' => 200,
            'data' => $result,
        ]);
    }

    public function getRanking(Request $request)
    {
        $ladder = [];
        $exams = [];
        $examCount = 0;
        $percentages = null;
        if ($request->type == 'day') {
            $exam = Exam::query()
              ->find($request->exam_id);
            $ladder = Result::query()
                ->with(['user' => function ($q) {
                    $q->select('id', 'name', 'avatar');
                }])
                ->where('exam_id', $request->exam_id)
                ->where(function ($q) use ($exam) {
                    $q->where('created_at', '>=', $exam->time_start)
                        ->where('created_at', '<=', $exam->time_end);
                })
                ->orderBy('total_score', 'desc')
                ->orderBy('submit_at')
                ->get(['id', 'user_id', 'exam_id', 'total_score', 'submit_at']);
        }
        if ($request->type == 'all') {
          $exams = Exam::query()
            ->where('type', 3)
            ->where('course', $request->level);
          if ($request->from) {
            $exams->where('time_start', '>=', $request->from . ' 00:00:00');
          }
          if ($request->to) {
            $exams->where('time_end', '<=', $request->to . ' 23:59:59');
          }
          $ladder = Result::query()
            ->selectRaw('SUM(total_score) as total_point, id, user_id, exam_id, total_score, submit_at')
            ->with(['user' => function ($q) {
              $q->select('id', 'name', 'avatar');
            }])
            ->whereIn('exam_id',$exams->pluck('id')->toArray())

            ->whereHas('exams', function($q) use ($request) {
              $q->where('course', $request->level);
            })
            ->groupBy('user_id')
            ->orderByRaw('SUM(total_score) desc')
            ->get(['id', 'user_id', 'exam_id', 'total_score', 'submit_at']);
          $examCount = $exams->count();
        }
        return response()->json([
            'code' => 200,
            'exams' => $exams,
            'exam_count' => $examCount,
            'percentages' => $percentages,
            'data' => $ladder
        ]);
    }

    public function getHistory(Request $request)
    {
        $userId = Auth::user()->id;
        $date = $request->date;
        $data = Exam::query()
          ->with([
            'lessons.questions.answers',
            'lessons' => function ($q) {
              $q->where('status', 1);
            },
            'lessons.questions' => function ($q) {
              $q->orderBy('position');
            }
          ])
          ->with(['result' => function ($result) use ($date, $userId) {
            $result->where('user_id', $userId)->whereDate('created_at', $date)->whereNotNull('submit_at');
          }])
          ->where('type', 3)
          ->whereDate('time_start', Carbon::parse($date)->format('Y-m-d'))
          ->whereDate('time_end', Carbon::parse($date)->format('Y-m-d'))
          ->get();
        return response()->json([
            'code' => 200,
            'data' => $data,
        ]);
    }
}
