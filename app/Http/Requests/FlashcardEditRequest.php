<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class FlashcardEditRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'jp_lang' => 'required',
            'type' => 'required',
            'image' => 'mimes:jpg,jpeg,png,gif',
            'audio' => 'mimetypes:audio/mp3,audio/mpeg,audio/mpga,video/mp4'
        ];
    }

    public function messages()
    {
        return [
            'image.mimes'  => 'Vui lòng upload đúng định dạng ảnh (jpg, png, gif)',
            'audio.mimetypes'  => 'Vui lòng upload đúng định dạng mp3',
            'vi_lang.required' => 'Vui lòng nhập',
            'jp_lang.required' => 'Vui lòng nhập',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'status' => 'error',
            'detail' => $validator->getMessageBag()
        ]));
    }
}
