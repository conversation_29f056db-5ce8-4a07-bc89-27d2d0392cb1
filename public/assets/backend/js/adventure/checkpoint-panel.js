"use strict";function _typeof(e){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),o.push.apply(o,n)}return o}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(o),!0).forEach(function(t){_defineProperty(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):ownKeys(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e}function _defineProperty(e,t,o){return t=_toPropertyKey(t),t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"===_typeof(t)?t:String(t)}function _toPrimitive(e,t){if("object"!==_typeof(e)||null===e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!==_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?_arrayLikeToArray(e,t):void 0}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}Vue.component("lesson-checkpoint-panel",{template:"#lesson-checkpoint-panel-template",props:["course","path","permission"],data:function(){return{url:window.location.origin,groups:[],lessons:[],checkpoints:{},checkpointsByKey:[],choseLessons:[],choseType:void 0,editingKey:void 0,keyToChange:void 0,categories:[],selectedCategoryId:void 0,selectedGroupId:void 0,editingCheckpoint:{},showingGroup:{},loading:!1,showModal:!1,showCheckpointModal:!1,draggable:!1,playbackRate:[.75,1,1.25,1.5,2],formData:{id:void 0,lesson_id:0,path_id:1,key:1,status:1}}},computed:{checkpointList:function(){return _.omitBy(this.checkpoints,_.isEmpty)}},watch:{showModal:function(e){e&&this.getCategories(),this.getGroupLessons()},selectedCategoryId:function(e){e&&this.getGroups()},selectedGroupId:function(e){e&&this.getGroupLessons()},path:{deep:!0,immediate:!0,handler:function(e){e.id&&this.getCheckpoints()}}},mounted:function(){},methods:{getCategories:function(){var e=this,t={courseId:this.course.id};$.post(this.url+"/backend/lesson-categories",t,function(t){200==t.code&&(e.categories=t.data,e.selectedCategoryId=t.data[0].id)})},getCheckpoints:function(){var e=this,t={pathId:this.path.id};$.get(this.url+"/backend/adventure/checkpoints",t,function(t){200==t.code&&(e.checkpoints=t.data)})},getGroups:function(){var e=this,t={id:this.selectedCategoryId};$.post(this.url+"/backend/lesson-groups",t,function(t){200==t.code&&(e.groups=t.data.groups,e.selectedGroupId=t.data.groups[0].id)})},getGroupLessons:function(){var e=this,t={pathId:this.path.id,id:this.selectedGroupId};$.post(this.url+"/backend/lesson-groups/get-reserved-lessons",t,function(t){200==t.code&&(e.lessons=t.data,t.data.length>0&&(e.formData.lesson_id=t.data[0].id))})},openCheckpointForm:function(){this.showModal=!0},printLessonIcon:function(e){var t="";switch(e){case"docs":t="docb.png";break;case"video":t="videob.png";break;case"test":t="quizb.png";break;case"flashcard":t="fcb.png";break;default:t="docb.png"}return t},changeLessonSpeed:function(e,t){var o=this,n={id:t.id,speed:e.target.value};$.post(o.url+"/backend/lesson-groups/change-lesson-speed",n,function(e){200===e.code?o.lessons=o.lessons.map(function(t){return t.id===e.data.id&&(t.default_speed=e.data.default_speed),t}):alert("Thất bại")})},changeLessonType:function(){var e=this;if(e.choseType&&e.choseLessons.length>0){var t={ids:_toConsumableArray(e.choseLessons),choseType:e.choseType};$.post(e.url+"/backend/lesson/change-lesson-type",t,function(t){200===t.code?(e.choseLessons=[],e.choseType=void 0,e.lessons=e.lessons.map(function(e,o){return e[o].type=t.data[o].type,e}),alert("Chuyển thành công")):alert("Chuyển thất bại")})}},changeCheckpointKey:function(){var e=this;if(e.keyToChange&&e.choseLessons.length>0){var t={ids:_toConsumableArray(e.choseLessons),keyToChange:e.keyToChange};$.post(e.url+"/backend/adventure/checkpoints/change-key",t,function(t){if(200===t.code){e.checkpointsByKey=e.checkpointsByKey.filter(function(t){return!e.choseLessons.includes(t.id)});var o=e.checkpoints[e.editingKey].filter(function(t){return!e.choseLessons.includes(t.id)});0===o.length?_.unset(e.checkpoints,e.editingKey):_.set(e.checkpoints,e.editingKey,o),_.has(e.checkpoints,e.keyToChange)&&e.checkpoints[e.keyToChange].length>0?(o=e.checkpoints[e.keyToChange].concat(t.data),_.set(e.checkpoints,e.keyToChange,o)):_.set(e.checkpoints,e.keyToChange,t.data),this.editingKey=void 0,e.choseLessons=[]}else alert("Chuyển thất bại")})}},checkAllLesson:function(e){var t=this,o=e.target.checked;o?t.choseLessons=t.checkpointsByKey.map(function(e){return e.id}):t.choseLessons=[],console.log("array --> ",t.choseLessons)},checkOneLesson:function(e,t){var o=this,n=e.target.checked;n?o.choseLessons.push(t):o.choseLessons=o.choseLessons.filter(function(e){return e!==t}),console.log("array --> ",o.choseLessons)},toggleHideLessonTitle:function(e){var t=this,o={id:e.id};$.post(t.url+"/backend/lesson/toggle-secret",o,function(e){200===e.code?t.lessons=t.lessons.map(function(t){return t.id===e.data.id&&(t.is_secret=e.data.is_secret),t}):alert(e.msg)})},onChangeCheckbox:function(e,t){var o=this;o.formData[t]=e.target.checked?1:0},onDragLessonEnd:function(){var e=this,t=e.checkpointsByKey.map(function(e){return e.id}),o={ids:t};$.post(e.url+"/backend/adventure/checkpoints/sort",o,function(t){200===t.code&&(e.checkpointsByKey=t.data)})},closeCheckpointModal:function(){var e=this;e.checkpointLessons=[],e.showCheckpointModal=!1},openCheckpointModal:function(e,t){var o=this;o.editingKey=t;var n={pathId:e[0].path_id,key:t};o.showCheckpointModal=!1,$.post(o.url+"/backend/adventure/checkpoints/get-by-key",n,function(e){200===e.code&&(o.checkpointsByKey=e.data,o.showCheckpointModal=!0)})},onDragEnd:function(){var e=this,t=e.groups.map(function(e){return e.id}),o={course_id:e.category.course_id,lesson_category_id:e.category.id,ids:t};$.post(e.url+"/backend/lesson-groups/apply-sorting",o,function(t){200==t.code&&(e.groups=t.data.map(function(e){return e}))})},changeCheckpointStatus:function(e,t){var o=this,n={id:e.id,status:t};$.post(o.url+"/backend/adventure/checkpoints/change-status",n,function(e){200===e.code&&(o.checkpointsByKey=o.checkpointsByKey.map(function(t){return t.id===e.data.id&&(t.status=e.data.status),t}))})},changeStatus:function(e,t){var o=this,n={id:e.id,show:t};$.post(o.url+"/backend/lesson-groups/change-status",n,function(e){200===e.code&&(o.groups=o.groups.map(function(t){return t.id===e.data.id&&(t.show=e.data.show),t}))})},saveForm:function(){var e=this,t=new FormData;t.append("id",e.formData.id),t.append("lesson_id",e.formData.lesson_id),t.append("path_id",e.path.id),t.append("key",e.formData.key),t.append("status",e.formData.status),e.formData.id?e.updateCheckpoint(t):e.addCheckpoint(t)},editCheckpoint:function(e){this.editingCheckpoint=e},addCheckpoint:function(e){var t=this;$.ajax({url:t.url+"/backend/adventure/checkpoints/add",type:"POST",data:e,processData:!1,contentType:!1,success:function(e){200==e.code?(e.data.path_id==t.path.id&&(t.checkpoints[e.data.key]?t.checkpoints[e.data.key].push(e.data):t.checkpoints=_.omitBy(Object.assign(t.checkpoints,_defineProperty({},e.data.key,[e.data])),_.isEmpty)),t.closeModal()):alert(e.msg)}})},updateCheckpoint:function(e){var t=this;$.ajax({url:t.url+"/backend/lesson-groups/update-group",type:"POST",data:e,processData:!1,contentType:!1,success:function(e){200===e.code?(e.data.lesson_category_id!==t.category.id?t.groups=_.remove(t.groups,function(t){return t.id!==e.data.id}):t.groups=t.groups.map(function(t){return t.id===e.data.id&&(t=_objectSpread({},e.data)),t}),t.closeModal()):alert(e.msg)}})},deleteCheckpointByKey:function(e){var t=this,o=window.confirm("Xác nhận xoá nhóm bài học cùng với toàn bộ bài học trong nhóm?");if(o){var n={pathId:e.point[0].path_id,key:e.key};$.post(t.url+"/backend/adventure/checkpoints/delete-by-key",n,function(o){200===o.code&&(t.checkpoints=_.omit(t.checkpoints,e.key))})}},deleteCheckpoint:function(e){var t=this,o=window.confirm("Xác nhận xoá nhóm bài học cùng với toàn bộ bài học trong nhóm?");if(o){var n={id:e.id};$.post(t.url+"/backend/adventure/checkpoints/delete",n,function(o){200===o.code&&(t.checkpointsByKey=t.checkpointsByKey.filter(function(t){return t.id!==e.id}))})}},setFormStatus:function(e){var t=this;t.formData.status=parseInt(e.target.value)},closeModal:function(){var e=this;e.showModal=!1},getGroupByCategory:function(e){var t=this;t.loading=!0;var o={course_id:e.course_id,id:e.id};$.post(t.url+"/backend/lesson-groups",o,function(e){200===e.code&&(t.groups=_toConsumableArray(e.data.groups),t.categories=_toConsumableArray(e.data.categories),t.loading=!1)})}}});