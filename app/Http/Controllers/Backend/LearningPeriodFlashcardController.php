<?php

namespace App\Http\Controllers\Backend;

use App\Http\Models\LearningCheckpoint;
use App\Http\Models\LearningPeriodFlashcard;
use App\Http\Models\LearningPeriodGroup;
use App\Http\Models\Lesson;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class LearningPeriodFlashcardController extends Controller
{
  public function __construct()
  {
    $this->middleware('auth:admin');
  }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index($groupId)
    {
        $group = LearningPeriodGroup::query()->find($groupId);
        $courseLessons = LearningCheckpoint::query()
            ->with('lesson')
            ->with('path')
            ->whereHas('path', function ($q) use ($group) {
                $q->where('course_id', $group->course_id);
            })
            ->whereHas('lesson', function ($q) {
                $q->where('type', 'flashcard');
            })
            ->where('status', 1)
            ->get();
        $lessons = LearningPeriodFlashcard::query()->with('lesson')->where('group_id', $groupId)->orderBy('sort')->get();
        $lessonIds = LearningPeriodFlashcard::query()->with('lesson')->with('group')->whereHas('group', function ($q) use ($group) {
            $q->where('course_id', $group->course_id);
        })->get()->map(function ($lesson) {
            return $lesson->lesson->id;
        })->toArray();
        $courseLessonIds = $courseLessons->map(function ($lesson) {
            return $lesson->lesson->id;
        })->toArray();
        $availableLessonIds = array_diff($courseLessonIds, $lessonIds);
        $availableLessons = LearningCheckpoint::query()->with('lesson')->whereHas('lesson', function($q) use ($availableLessonIds) {
            $q->whereIn('id', $availableLessonIds);
        })->get();
        return response()->json([
            'code' => 200,
            'data' => [
                'available' => $availableLessons,
                'lessons' => $lessons
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request, $groupId)
    {
        $ids = $request->ids;
        $maxSort = LearningPeriodFlashcard::query()->where('group_id', $groupId)->max('sort');
        if (!$maxSort) $sort = 1;
        else $sort = $maxSort + 1;
        foreach ($ids as $id) {
            $dumpData = [
                'group_id' => $groupId,
                'lesson_id' => $id,
                'sort' => $sort,
                'status' => 1,
            ];
            LearningPeriodFlashcard::query()->create($dumpData);
            $sort += 1;
        }
        $groups = LearningPeriodFlashcard::query()->with('lesson')->where('group_id', $groupId)->orderBy('sort')->get();
        return response()->json([
            'code' => 200,
            'data' => $groups
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        //
        LearningPeriodGroup::query()->where('id', $id)->update($request->only('title', 'sort', 'status'));
        $group = LearningPeriodGroup::find($id);
        return response()->json([
            'code' => 200,
            'data' => $group
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $checkpoint = LearningPeriodFlashcard::query()->find($id);
        $checkpoint->delete();
        return response()->json([
            'code' => 200
        ]);
    }

    public function sort(Request $request, $courseId)
    {
        $ids = $request->ids;
        for ($i = 0; $i < count($ids); $i++) {
            $checkpoint = LearningPeriodFlashcard::query()->find($ids[$i]);
            if ($checkpoint) {
                $checkpoint->sort = $i + 1;
                $checkpoint->save();
            }
        }
        $lessons = LearningPeriodFlashcard::query()->whereIn('id', $ids)->orderBy('sort', 'ASC')->get();
        return response()->json([
            'code' => 200,
            'data' => $lessons
        ]);
    }
}
