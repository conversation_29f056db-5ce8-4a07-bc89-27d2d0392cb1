<?php

namespace App\Http\ModelsFrontend\ThiThu;

use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;
class Schedule extends Model
{
    protected $connection = 'mysql2';
    protected $table = 'schedule';

  /**
   * Prepare a date for array / JSON serialization.
   *
   * @param  \DateTimeInterface  $date
   * @return string
   */
  protected function serializeDate(DateTimeInterface $date)
  {
    return $date->format('Y-m-d H:i:s');
  }

}
