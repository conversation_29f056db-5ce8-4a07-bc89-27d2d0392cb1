@extends('frontend._layouts.default')
@section('title')
    {{ $lesson->name }} - Dungmori
@stop
@section('description')
    Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất
@stop
@section('keywords')
    Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất
@stop
@section('image')
    {{ url('cdn/course/default') }}/{{ $lesson->avatar_name }}
@stop
@section('author')
    DUNGMORI
@stop

@section('header-css')
    <link rel="stylesheet" href="{{ asset('/plugin/videojs/video.css') }}">
    <link rel="stylesheet" href="{{ asset('css/course/basic.css') }}">
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <link rel="stylesheet" href="//unpkg.com/element-ui@2.15.13/lib/theme-chalk/index.css">
    <script src="{{ asset('/plugin/chartjs447/chart.js') }}"></script>
    <link rel="stylesheet" type="text/css" href="/plugin/slick/slick.css"/>
    <link rel="stylesheet" type="text/css" href="/plugin/slick/slick-theme.css"/>

    <script
        type="module"
        src="https://cdn.jsdelivr.net/npm/media-chrome@3/+esm"
    ></script>

    <style>
        .pc-header .logo2 {
            display: none !important;

        }

        .logo3 {
            display: block !important;

            img {
                width: 45px;
            }
        }

        .slide-dialog-instruction .slick-slide {
            height: unset;
        }

        .run-text span {
            display: inline-block;
            position: relative;
            white-space: pre;
        }

        .run-text span::before {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: var(--width, 0%);
            height: 100%;
            background: #EF6D13;
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            overflow: hidden;
            transition: width 0s;
        }
        .pulse-container {
            position: fixed;
            bottom: -40px;
            left: -37%;
            width: 100vw;
            height: 70ch;
            overflow: hidden;
        }

        .pulse-circle {
            width: 627px;
            height: 272px;
            background: #FFB98F;
            border-radius: 50%;
            position: absolute;
            bottom: -194px;
            left: 32%;
            transition: transform 0.1s ease-in-out;
            filter: blur(35px);
        }
    </style>

    @if($lesson->type === 'flashcard')
        <link rel="stylesheet" href="{{ asset('css/module/flashcard.css') }}">
    @endif
@stop
@section('footer-js')
    <script src="{{ asset('/plugin/axios/axios.min.js') }}"></script>
    <script defer src="{{ asset('/plugin/videojs/video.min.js') }}"></script>
    <script defer src="{{ asset('assets/js/course/basic.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script src="{{ asset('plugin/pdfjs/pdf.min.js') }}"></script>
    <script src="{{ asset('assets/js/custom_pdf_view.js') }}?{{ filemtime('assets/js/custom_pdf_view.js') }}"></script>
    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script>
        var courseObject = @json($course);

        var checkFocusTab = true;
        $(document).ready(function(){
            window.onfocus = function() { checkFocusTab = true; };
            window.onblur = function() { checkFocusTab = false; };
        });

        if (courseObject) {
            var auth = @json(Auth::user());
            if (auth && !auth.is_tester && !auth.is_assistant) {
                setInterval(() => {
                    if (checkFocusTab) {
                        switch (courseObject.id) {
                            case 39:
                                ga('send', 'event', 'thoi_gian_hoc', 'hoc_n5', 'thoi_gian_hoc');
                                break;
                            case 40:
                                ga('send', 'event', 'thoi_gian_hoc', 'hoc_n4', 'thoi_gian_hoc');
                                break;
                        }
                    }
                }, 60000); // 60s
            }
        }

        const videoName = "{{ $lesson->video ? $lesson->video->video_name : '' }}";
        const videoRenderType =
            "{{ $lesson->video && $lesson->video->video_info ? $lesson->video->video_info->render_type : '' }}";
        const menuData = @json($menuData);
        console.log(`menuData: `, menuData)
        const currentLesson = @json($currentLesson);
        const course = @json($currentCourse);
        const course_id = course.id;
        const nextLesson = @json($nextLesson);
        console.log(`$nextLesson: `, nextLesson);
        const nextToLesson = @json($nextToLesson);
        const allLesson = @json($allLesson);
        let nextLessonCurrentGroup = @json($nextLessonCurrentGroup);
        {{--const jpServer = "{{ env('VIDEO_SERVER_URL') }}";--}}
        const jpServer = "https://tokyo-v2.dungmori.com";
        const vnServer = "https://vn.dungmori.com";
        const authUser = {
            id: "{{ auth()->check() ? auth()->user()->id : '' }}",
            name: "{{ auth()->check() ? auth()->user()->name : '' }}",
            avatar: "{{ auth()->check() && auth()->user()->avatar ? url('cdn/avatar/default/' . auth()->user()->avatar) : '/images/icons/default-user.png' }}",
            isTester: "{{ auth()->check() ? auth()->user()->is_tester : false }}",
        }
        let lesson = @json($lesson);
        let userLesson = @json(\Illuminate\Support\Facades\Auth::user());
        let isUnlock = @json($isUnlock)

        const dataLesson = @json($contentLesson);
        console.log(`currentLesson: `, currentLesson)
        console.log(`dataLesson: `, dataLesson)
        const currentAnswer = dataLesson !== null ? dataLesson.component[0] : {};
        const idCurrentAnswer = dataLesson !== null && dataLesson.component.length > 0 ? dataLesson.component[0].id : -1;
        const userResult = []

        $(document).ready(function () {
            $(document).on("click", ".buttonAudio", function () {
                let id = $(this).data("id");
                console.log(`id: `, id);
                let audio = document.getElementById(id);
                audio.play();
            });

            $('.slide-dialog-instruction').slick({
                slidesToShow: 1,
                slidesToScroll: 1,
                speed: 500,
                dots: true,
                arrows: false,
            });
        });

        @php
            // Xử lý logic phức tạp trong PHP
            $isShowTabBtn = !in_array($lesson->type, ['exam', 'last_exam'])
                            && $lesson->components->whereIn('type', \App\Http\Models\LessonToTask::COMPONENT_TYPES_EXERCISE)->isNotEmpty()
                            && ($lesson->documents->isNotEmpty() || $lesson->video);
        @endphp

        let isShowTabBtn = @json($isShowTabBtn);
        console.log('isShowTabBtn: ', isShowTabBtn)
    </script>
    <script src="{{ asset('assets/js/course/lesson-new.js') }}?{{filemtime('assets/js/course/lesson-new.js')}}"></script>
@stop
@section('content')
{{--    {{ dd(auth()->user()) }}--}}
{{--    {{ dd((!in_array($lesson->type, ['exam', 'last_exam']) && $lesson->components->whereIn('type', [3, 13])->isNotEmpty() && ($lesson->documents->isNotEmpty() || $lesson->video))) }}--}}
    @php
        $assetVersion = '1.0.0';
        $isLocked = !$isUnlock && (!auth()->check() || $lesson->price_option != 0);
    @endphp

    <div
        class="bg-[#F4F5FA] lesson {{ in_array($currentLesson['type'], ['exam','last_exam']) ? '' : 'md:p-10' }} min-h-[calc(100vh-60px)] md:min-h-[calc(100vh-76px)]"
        id="lesson-basic-container"
        v-cloak
    >
        @if ($isShowTabBtn)

            <div class="fixed lg:top-3.5 top-1.5 lg:left-[41.5%] left-[34.5%] z-[100] hidden md:block" id="tabList_header">
                <div class="flex justify-center lesson-main-container sp:w-full duration-500" id="tabList">
                    <div class="tab-content tabs @if(\Illuminate\Support\Facades\Auth::check()) bg-[#C1EACA] @else bg-[#E6E6E6] @endif p-1 rounded-full text-base flex w-[250px]">
                        <div @click="tabActive = 'tabLesson'" id="btn_header_tab_lesson"
                             :class="`font-averta-regular  tab py-2 text-center ${tabActive === 'tabLesson' ? 'bg-white text-black rounded-full disabled' : 'text-[#757575]'} w-1/2 cursor-pointer nav-item`"
                             data-tab="tabLesson">
                            Bài học
                        </div>
                        <div @click="tabActive = 'tabExercise'" id="btn_header_tab_exercise"
                             :class="`font-averta-regular tab py-2 text-center w-1/2 cursor-pointer nav-item ${tabActive === 'tabExercise' ? 'bg-white text-black rounded-full disabled' : 'text-[#757575]'} `"
                             data-tab="tabExercise">
                            <svg v-if="isFirstTabExercise" width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="3" cy="3" r="3" fill="#EF6D13"/>
                            </svg>
                            Bài tập
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <div class="mx-auto">
            <div id="lesson-basic-tutorial">
                @include('frontend.course.components.lesson-tutorial')
            </div>
            <div class="fixed z-100 {{ in_array($currentLesson['type'], ['exam','last_exam']) ? 'top-[10px]' : 'top-[22px]' }} left-[8.5%] z-[100] sp:hidden">
                <div class="flex items-center text-[#07403F] font-beanbag cursor-pointer">
                    <div class="btn-course text-[24px]" @click="setRedirectModal(null, { stage: @json($currentLesson['category_id']) }, 'exit')">{{ $course->name }}</div>
                    {{--                <div class="ml-3 bg-[#FFF1C2] text-xs h-[20px] flex items-center justify-center px-4 rounded-full">--}}
                    {{--                    {{ $course->percent ?? 0 }}%--}}
                    {{--                </div>--}}
                </div>
                @if(in_array($lesson->type, ['exam', 'last_exam']))
                    <div class="font-beanbag uppercase text-[#07403F] flex items-center gap-1">
                        {{ $lesson->name }}
                        @if($currentLesson['require'])
                            <img
                                class="max-h-[8px] relative -mt-1"
                                :src="`/images/icons/require.png`"
                                alt="require.png"
                            />
                        @endif
                    </div>
                @endif
            </div>
            <div id="lesson-list">
                <container is-unlock="{{ $isUnlock }}" stage-name="{{ $stageName }}" type="{{ $lesson->type }}" :exam-stage="examStage" @active-changed="handleContainerActive($event)">
                </container>
            </div>

            @if(in_array($lesson->type, ['exam', 'last_exam', 'flashcard']))
                @if ($isLocked || !auth()->check())
                    <!-- Lớp overlay yêu cầu mua khóa học -->
                    <div id="purchase-overlay"
                             class="absolute inset-0 flex flex-col items-center justify-center rounded-lg z-10">
                            <div class="flex items-center mb-[36px]">
                                <svg class="m-[7px]" width="16" height="16" viewBox="0 0 16 16" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M4 6.66634V5.33301C4 3.12634 4.66667 1.33301 8 1.33301C11.3333 1.33301 12 3.12634 12 5.33301V6.66634"
                                        stroke="#07403F" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round"/>
                                    <path
                                        d="M7.99992 12.3333C8.92039 12.3333 9.66659 11.5871 9.66659 10.6667C9.66659 9.74619 8.92039 9 7.99992 9C7.07944 9 6.33325 9.74619 6.33325 10.6667C6.33325 11.5871 7.07944 12.3333 7.99992 12.3333Z"
                                        stroke="#07403F" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round"/>
                                    <path
                                        d="M11.3333 14.667H4.66659C1.99992 14.667 1.33325 14.0003 1.33325 11.3337V10.0003C1.33325 7.33366 1.99992 6.66699 4.66659 6.66699H11.3333C13.9999 6.66699 14.6666 7.33366 14.6666 10.0003V11.3337C14.6666 14.0003 13.9999 14.667 11.3333 14.667Z"
                                        stroke="#07403F" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round"/>
                                </svg>

                                <p
                                    class="mb-0 text-[20px] font-semibold text-center font-beanbag text-[#07403F] leading-[2px]">
                                    @if (!auth()->check())
                                        Bạn cần đăng nhập để học hoặc bình luận
                                    @elseif($isLocked)
                                        Mua khóa học để học ngay
                                    @endif
                                </p>
                            </div>

                            @if (!auth()->check())
                                <div class="flex items-center justify-center font-beanbag">
                                    <a data-fancybox data-animation-duration="300" data-src="#auth-container"
                                       style="margin-right: 10px;">
                                        <div class="font-beanbag text-[20px] bg-[#57D061] mx-5 sp:mx-1 sp:text-base !text-[#07403F] border border-white rounded-full py-2 w-[170px] text-center uppercase cursor-pointer"
                                             onclick="swichTab('login')">Đăng nhập
                                        </div>
                                    </a>
                                    <a data-fancybox data-animation-duration="300" data-src="#auth-container">
                                        <div class="bg-[#EC6E23] mx-5 sp:mx-1 text-[20px] sp:text-base !text-white border border-white rounded-full py-2 w-[170px] text-center uppercase cursor-pointer"
                                             onclick="swichTab('register')">Đăng ký
                                        </div>
                                    </a>

                                </div>
                            @elseif($isLocked)
                                <button @click="buyNow(course.id)" href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}"
                                   class="bg-[#EC6E23] text-white text-[17px] font-beanbag px-6 py-2 rounded-full border-white border-[1px]">
                                    MUA NGAY
                                </button>
                            @endif
                        </div>
                @elseif($currentLesson['type'] == 'flashcard')
                    <div id="lesson-main" class="mx-auto max-w-[90%] sp:max-w-full h-[calc(100vh-156px)]">
                        <div class="lesson-main-container sp:w-full"
                            :class="isActive ? 'w-2/3 desktop:max-w-[calc(100%-420px)]' : ''"
                        >
                            <flash-card :active="isActive" :data-lesson="dataLesson" @open-comment-tab="openCommentTab($event)"></flash-card>
                        </div>
                    </div>
                @else
                    <lesson-exam :lesson="lesson" :tasks="lesson.components" :unlock="isUnlock" @stage-changed="handleStageChange($event)" :active="isActive"></lesson-exam>
                @endif
            @else

                <div class="transform transition-transform mx-auto max-w-[90%] sp:max-w-full pt-5 sp:mt-10 font-averta-regular duration-500" id="lesson-main">
                @if ($isShowTabBtn)
                    <div class="mb-[24px] flex justify-center lesson-main-container w-2/3 desktop:max-w-[calc(100%-420px)] sp:w-full duration-500" id="tabList"  ref="box">
                        <div class="tab-content tabs @if(\Illuminate\Support\Facades\Auth::check()) bg-[#C1EACA] @else bg-[#E6E6E6] @endif p-1 rounded-full text-base flex w-[250px]">
                            <div @click="tabActive = 'tabLesson'" :class="`font-averta-regular  tab py-2 text-center ${tabActive === 'tabLesson' ? 'active-tab bg-white text-black rounded-full disabled' : 'text-[#757575]'} w-1/2 cursor-pointer nav-item`"
                                 data-tab="tabLesson">
                                Bài học
                            </div>
                            <div @click="tabActive = 'tabExercise'" id="button_tab_exercise" :class="`font-averta-regular tab py-2 text-center  w-1/2 cursor-pointer nav-item ${tabActive === 'tabExercise' ? 'active-tab bg-white text-black rounded-full disabled' : 'text-[#757575]'}`"
                                 data-tab="tabExercise">
                                <svg v-if="isFirstTabExercise" width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="3" cy="3" r="3" fill="#EF6D13"/>
                                </svg>
                                Bài tập
                            </div>
                        </div>
                    </div>
                @endif

                    <div v-show="tabActive === 'tabLesson'" id="tabLesson" class="tab-content slide-in lesson-main-container w-2/3 desktop:max-w-[calc(100%-420px)] sp:w-full duration-500">
                        @include('frontend.course.components.lesson-new-video')
                    </div>
                    <div id="tabExercise"
                         v-show="tabActive === 'tabExercise'"
                         class="tab-content slide-in duration-500">
                        <div v-show="windowWidth > 640" class="slide-in xl:w-2/3 w-full duration-500 lesson-main-container md:block">
                            <div v-if="idCurrentAnswer !== -1"
                                 class="main-container-wrap @if($isLocked) opacity-30 pointer-events-none @endif">
                                <template v-if="statusAnswer !== 'result'">

                                    <!-- Main Content Section -->
                                    <!-- Progress Bar -->
                                    <div class="py-2 mt-4 font-beanbag">
                                        <div class="mx-auto flex items-center justify-between">
                                            <div class="text-sm text-gray-500" id="progress_text">
                                                0/{{ $contentLesson->component->count() }}</div>
                                            <div class="w-full mx-4 bg-gray-300 rounded-full border ml-5 mr-5" style="height: 6px">
                                                <div class="bg-green-500 rounded-full border-green-500" id="progress_bar"
                                                     style="width: 0%; height: 5px; background-color: #2CD868"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Main Content -->
                                    <main class="mx-auto mt-8 flex-1 font-beanbag">
                                        <div class="bg-[#F4F5FA] rounded-lg p-6">
                                            <!-- Lesson Title -->
                                            <div class="flex justify-content-space-between align-items-center mb-[24px]">
                                                <div class="mb-0 font-averta-semibold">
                                                    <div class="icon-require flex">
                                                        @if ($lesson->name_html)
                                                            <h1 class="
                                                            text-[18px] font-bold text-black">{!! $contentLesson->name_html !!}</h1><span>
                                                        @else
                                                            <h1 class="text-[18px] font-bold text-black">{{ $contentLesson->name }}</h1>
                                                        @endif
                                                        @if($currentLesson['require'])
                                                            <img
                                                                class="max-h-[12px] relative ml-[5px]"
                                                                :src="`/images/icons/require.png`"
                                                                alt="require.png"
                                                            />
                                                        @endif
                                                    </div>
                                                    <p id="type_question" class="text-sm mt-2 text-black font-averta-regular">
                                                        <span v-if="currentAnswer.type === 13">Điền từ bằng chữ Hiragana phù hợp vào chỗ trống</span>
                                                        <span v-else-if="currentAnswer.type === 15">Hãy ghép cặp phù hợp ở 2 cột</span>
                                                        <span v-else-if="currentAnswer.type === 14">Sắp xếp các thành phần đã cho thành từ hoặc câu hoàn chỉnh</span>
                                                        <span v-else-if="currentAnswer.type === 16">Nghe và lặp lại theo đúng ngữ điệu</span>
                                                        <span v-else>Chọn đáp án đúng</span>
                                                    </p>
                                                </div>
                                                <div class="relative group" v-if="([13,14,15].includes(currentAnswer.type) && currentAnswer.value.suggest != null) || (currentAnswer.type === 3 && currentAnswer.suggest != null)">
                                                    <button id="button_suggest"
                                                            class="bg-[#F4F5FA] w-auto text-[14px] text-[#57D061] border-[2px] border-[#57D061] text-bold px-4 py-1 rounded-full mr-2.5 transform -translate-x-10 transition duration-500 ease-in-out">
                                                        <svg width="16" height="14" viewBox="0 0 16 14" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path opacity="0.4"
                                                                  d="M3.73935 9.33317C3.81101 9.04734 3.68071 8.639 3.45268 8.43484L1.8695 7.01734C1.37435 6.574 1.17889 6.1015 1.32222 5.69317C1.47207 5.28484 1.93465 5.00484 2.62526 4.89984L4.65798 4.5965C4.95116 4.54984 5.3095 4.3165 5.44632 4.07734L6.56692 2.06484C6.89268 1.48734 7.33571 1.1665 7.81783 1.1665C8.29995 1.1665 8.74298 1.48734 9.06874 2.06484L10.1893 4.07734C10.274 4.229 10.45 4.37484 10.6389 4.474L3.62207 10.7565C3.53086 10.8382 3.3745 10.7623 3.40056 10.6457L3.73935 9.33317Z"
                                                                  fill="#57D061"/>
                                                            <path
                                                                d="M12.1832 8.4351C11.9487 8.6451 11.8184 9.0476 11.8966 9.33344L12.3461 11.0893C12.535 11.8184 12.4178 12.3668 12.0138 12.6293C11.8509 12.7343 11.6555 12.7868 11.4275 12.7868C11.0952 12.7868 10.7043 12.6759 10.2743 12.4484L8.36534 11.4334C8.06564 11.2759 7.57049 11.2759 7.2708 11.4334L5.36186 12.4484C4.63867 12.8276 4.01974 12.8918 3.62231 12.6293C3.47246 12.5301 3.36171 12.3959 3.29004 12.2209L11.2125 5.1276C11.5122 4.85927 11.9356 4.73677 12.3461 4.80094L13.0041 4.9001C13.6947 5.0051 14.1573 5.2851 14.3072 5.69344C14.4505 6.10177 14.255 6.57427 13.7599 7.0176L12.1832 8.4351Z"
                                                                fill="#57D061"/>
                                                        </svg>
                                                        Gợi ý
                                                    </button>
                                                    <!-- Tooltip -->
                                                    <div
                                                        :class="`max-w-[350px] 2xl:max-w-[500px] absolute right-[150%] top-1/2 transform -translate-y-1/2 rounded-full text-black text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 overflow-hidden`">
                                                        <div class="flex items-center rounded-full">
                                                            <div :class="`px-4 py-2 bg-[#57D061] bg-green-300 rounded-full font-gen-jyuu-gothic break-words`"
                                                                 :style="{ width: renderWidthTooltipSuggest(currentAnswer) }"
                                                                 v-html="currentAnswer.type === 13 || currentAnswer.type === 14 ? currentAnswer.value.suggest : currentAnswer.suggest">
                                                            </div>
                                                            <div class="top right-[-9px] top-[50%]">
                                                                <svg fill="#57D061" width="12" height="12"
                                                                     viewBox="8 -2.56 37.12 37.12" version="1.1"
                                                                     xmlns="http://www.w3.org/2000/svg" stroke="#57D061"
                                                                     transform="matrix(1, 0, 0, 1, 0, 0)rotate(0)"
                                                                     stroke-width="0.00032">
                                                                    <g id="SVGRepo_iconCarrier">
                                                                        <title>play</title>
                                                                        <path
                                                                            d="M5.92 24.096q0 1.088 0.928 1.728 0.512 0.288 1.088 0.288 0.448 0 0.896-0.224l16.16-8.064q0.48-0.256 0.8-0.736t0.288-1.088-0.288-1.056-0.8-0.736l-16.16-8.064q-0.448-0.224-0.896-0.224-0.544 0-1.088 0.288-0.928 0.608-0.928 1.728v16.16z"/>
                                                                    </g>
                                                                </svg>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            @foreach ($contentLesson->component as $key => $component)
                                                <div id="wrap-question-{{ $component->id }}"
                                                     class="wrap-question {{ !$loop->first ? 'hidden' : '' }} relative z-[1]"
                                                     data-id="{{ $component->id }}">
                                                    @if ($component->type === \App\Http\Models\LessonToTask::MULTIPLE_CHOICE)<!-- Câu trắc nghiệm -->
                                                        <!-- Question -->
                                                        <div
                                                            :class="`content-question mt-6 mb-15 leading-[1.61] ${checkAnswerAudio({{ $component }}) == 4 ? 'p-4 bg-white rounded-3xl shadow-lg' : ''} text-center ${ checkAnswerAudio({{ $component }}) != 1 ? 'max-w-[80%]' : ''} mx-auto ${checkAnswerAudio({{ $component }}) == 2 ? 'flex justify-content-center align-items-center' : ''} font-gen-jyuu-gothic text-black`">
                                                            <template v-if="checkComponentQuestion({{ $component }}) == 1">
                                                                <div class="bg-white shadow-md rounded-3xl px-[18px] py-[21px] w-full">
                                                                    <!-- Icon âm thanh -->
                                                                    <div class="flex">
                                                                        <svg  @click="toggleMp3(@json($component->id), 'question')"
                                                                             class="cursor-pointer" width="48" height="48"
                                                                             viewBox="0 0 48 48" fill="none"
                                                                             xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                                                fill="#4E87FF"
                                                                                :class="{ 'animate-opacity': (currentPlaying && currentPlaying.id === @json($component->id)) }"/>
                                                                            />
                                                                            <path
                                                                                d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                                                fill="#4E87FF"
                                                                                :class="{ 'animate-opacity-second': (currentPlaying && currentPlaying.id === @json($component->id)) }"/>
                                                                            <path opacity="0.4"
                                                                                  d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                                                  fill="#4E87FF"/>
                                                                            <path
                                                                                d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                                                fill="#4E87FF"/>
                                                                        </svg>
                                                                    </div>

                                                                    <!-- Văn bản Furigana -->
                                                                    <template v-if="checkAnswerAudio({{ $component }}) == 1">
                                                                        <div class="flex-1 text-center text-gray-800 mt-2 text-[20px]"
                                                                             v-html="getTagImg({{ $component }})">
                                                                        </div>
                                                                    </template>
                                                                    <template v-else>
                                                                        <div class="flex-1 text-center text-gray-800 mt-2 text-[20px]"
                                                                             v-html="getComponentFromQuestionStr({{ $component }})">
                                                                        </div>
                                                                    </template>
                                                                </div>
                                                            </template>
                                                            <template v-else-if="checkAnswerAudio({{ $component }}) == 2">
                                                                <button data-audioButton="audioButton-{{ $component->id }}"
                                                                        ref="audioButton-{{ $component->id }}"
                                                                        @click="toggleMp3(@json($component->id), 'question')"
                                                                        :class="[
                                                                        'flex items-center justify-center w-[100px] h-[100px] rounded-full bg-white text-blue-500 hover:bg-blue-100 flex-col',
                                                                        currentPlaying && currentPlaying.id === @json($component->id) ? 'animate-blink' : 'shadow-[0_0_15px_4px] shadow-[#4E87FF80]'
                                                                        ]">
                                                                    <!-- Icon loa -->
                                                                    <div
                                                                        class="w-[50px] h-[50px] bg-blue-500 rounded-full flex items-center justify-center">
                                                                        <svg width="100px" height="100px" viewBox="0 0 45 44"
                                                                             fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M33.7505 30.7083C33.4505 30.7083 33.1693 30.6166 32.9068 30.4333C32.288 29.975 32.1568 29.1133 32.6255 28.5083C35.5693 24.6766 35.5693 19.3233 32.6255 15.4916C32.1568 14.8866 32.288 14.025 32.9068 13.5666C33.5255 13.1083 34.4068 13.2366 34.8755 13.8416C38.5505 18.645 38.5505 25.355 34.8755 30.1583C34.5943 30.525 34.1818 30.7083 33.7505 30.7083Z"
                                                                                fill="#4E87FF" fill-opacity="0.5"
                                                                                :class="{ 'animate-opacity': currentPlaying && currentPlaying.id === @json($component->id) }"/>
                                                                            <path
                                                                                d="M37.1822 35.2917C36.8822 35.2917 36.6009 35.2001 36.3384 35.0167C35.7197 34.5584 35.5884 33.6967 36.0572 33.0917C41.0634 26.5651 41.0634 17.4351 36.0572 10.9084C35.5884 10.3034 35.7197 9.44172 36.3384 8.98339C36.9572 8.52505 37.8384 8.65339 38.3072 9.25839C44.0634 16.7567 44.0634 27.2434 38.3072 34.7417C38.0447 35.1084 37.6134 35.2917 37.1822 35.2917Z"
                                                                                fill="#4E87FF" fill-opacity="0.5"
                                                                                :class="{ 'animate-opacity-second': currentPlaying && currentPlaying.id === @json($component->id) }"/>
                                                                            <path opacity="0.4"
                                                                                  d="M29.5312 13.585V30.415C29.5312 33.5683 28.3688 35.9333 26.2875 37.07C25.4438 37.5283 24.5063 37.7483 23.5312 37.7483C22.0312 37.7483 20.4188 37.2533 18.7688 36.245L13.2938 32.89C12.9188 32.67 12.4875 32.5417 12.0562 32.5417H10.3125V11.4583H12.0562C12.4875 11.4583 12.9188 11.33 13.2938 11.11L18.7688 7.75499C21.5063 6.08666 24.1875 5.79333 26.2875 6.92999C28.3688 8.06666 29.5312 10.4317 29.5312 13.585Z"
                                                                                  fill="#4E87FF"/>
                                                                            <path
                                                                                d="M10.3125 11.4583V32.5417H9.375C4.8375 32.5417 2.34375 30.1033 2.34375 25.6667V18.3333C2.34375 13.8967 4.8375 11.4583 9.375 11.4583H10.3125Z"
                                                                                fill="#4E87FF"/>
                                                                        </svg>
                                                                    </div>
                                                                    <!-- Thời gian -->
                                                                    <span
                                                                        class="text-[#4E87FF] text-sm font-averta-bold text-blue-500">@{{ formattedTime }}</span>
                                                                </button>
                                                            </template>
                                                            <template v-else class="text-base">
                                                                <div v-html="dataLesson.component[@json($key)].value"></div>
                                                            </template>
                                                        </div>
                                                        <!-- End Question -->


                                                        <!-- Options -->
                                                        <div id="options"
                                                             class="grid grid-cols-2 gap-4 mb-15 max-w-[695px] mx-auto">
                                                            @foreach ($component->answers as $keyAnswer => $answer)
                                                                <!-- Đáp án là audio -->
                                                                <template v-if="checkAnswerAudio({{ $answer }}) == 3 || checkAnswerAudio({{ $answer }}) == 2">
                                                                    <label class="block">
                                                                        <input id="{{ $answer->id }}" type="radio"
                                                                               data-result="{{ $answer->grade }}" name="answer"
                                                                               value="{{ $answer->value }}" class="hidden"
                                                                               @click="selectOption({{ $answer }})">
                                                                        <span id="span-option-{{ $answer->id }}"
                                                                              data-question="{{ $component->id }}"
                                                                              data-answer="{{ $answer->id }}"
                                                                              data-result="{{ $answer->grade }}"
                                                                              class="text-bold span-option p-[12px] bg-white border-[#F4F5FA] border-[4px] rounded-full cursor-pointer flex align-items-center text-[#07403F]">
                                                                    <span>
                                                                        {{ $keyAnswer + 1 }}.
                                                                    </span>
                                                                <span
                                                                    class="ml-2 text-black font-gen-jyuu-gothic w-full text-center">
                                                                    <svg class="cursor-pointer" width="32"
                                                                         height="32" viewBox="0 0 48 48" fill="none"
                                                                         xmlns="http://www.w3.org/2000/svg">
                                                                        <path
                                                                            d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                                            fill="#4E87FF"
                                                                            :class="{ 'animate-opacity': currentPlaying && currentPlaying.id === @json($answer->id) && currentPlaying.type === 'option' }"
                                                                        />
                                                                        <path
                                                                            d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                                            fill="#4E87FF"
                                                                            :class="{ 'animate-opacity-second': currentPlaying && currentPlaying.id === @json($answer->id) && currentPlaying.type === 'option' }"
                                                                        />
                                                                        <path opacity="0.4"
                                                                              d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                                              fill="#4E87FF"/>
                                                                        <path
                                                                            d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                                            fill="#4E87FF"/>
                                                                    </svg>
                                                                </span>
                                                            </span>
                                                                    </label>
                                                                </template>

                                                                <template v-else>
                                                                    <label class="block">
                                                                        <input id="{{ $answer->id }}" type="radio"
                                                                               data-result="{{ $answer->grade }}" name="answer"
                                                                               value="{{ $answer->value }}" class="hidden"
                                                                               @click="selectOption({{ $answer }})">
                                                                        <span id="span-option-{{ $answer->id }}"
                                                                              data-question="{{ $component->id }}"
                                                                              data-answer="{{ $answer->id }}"
                                                                              data-result="{{ $answer->grade }}"
                                                                              :class="`span-option-default span-option p-4 bg-white border-[#F4F5FA] border-[4px] rounded-${checkAnswerAudio({{ $answer }}) === 4 ? '2xl' : 'full'} cursor-pointer flex items-center text-[#07403F]`">
                                                                              <span class="text-bold text-[16px] leading-[1.61]">
                                                                                {{ $keyAnswer + 1 }}.
                                                                              </span>
                                                                              <span
                                                                                  class="ml-2 text-black font-averta-semibold font-normal text-[16px] leading-[1.61]">
                                                                                  {!! $answer->value !!}
                                                                              </span>
                                                                        </span>
                                                                    </label>
                                                                </template>
                                                            @endforeach
                                                        </div>
                                                        <!-- End Options -->
                                                    @elseif($component->type === \App\Http\Models\LessonToTask::TYPE_FILL_IN_THE_BLANK)<!-- Câu điền từ -->
                                                        <!-- Question box -->
                                                        <div
                                                            class="grid items-stretch justify-items-stretch align-items-center w-full max-w-[80%] min-h-[250px] bg-white rounded-3xl shadow-lg p-6 mx-auto
{{--                                                        @if($component->value['audio'] !== null && $component->value['audio'] !== '' && ($component->value['img'] !== null || $component->value['img'] !== "") && (!isset($component->value['text_vi']) || (isset($component->value['text_vi']) && ($component->value['text_vi'] === "" || $component->value['text_vi'] === null))))--}}
{{--                                                            grid-rows-3--}}
{{--                                                        @endif--}}
                                                        ">
                                                            <!-- Icon âm thanh -->
                                                            @if($component->value['audio'] !== null && $component->value['audio'] !== '')
                                                                <div class="top-[18px] left-[21px] justify-self-start row-start-1 self-start">
                                                                    <div class="flex">
                                                                        <svg @click="toggleMp3(@json($component->id), 'question')"
                                                                             class="cursor-pointer" width="48" height="48"
                                                                             viewBox="0 0 48 48" fill="none"
                                                                             xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                                                fill="#4E87FF"
                                                                                :class="{ 'animate-opacity': currentPlaying && currentPlaying.id === @json($component->id) }"
                                                                            />
                                                                            <path
                                                                                d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                                                fill="#4E87FF"
                                                                                :class="{ 'animate-opacity-second': currentPlaying && currentPlaying.id === @json($component->id) }"
                                                                            />
                                                                            <path opacity="0.4"
                                                                                  d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                                                  fill="#4E87FF"/>
                                                                            <path
                                                                                d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                                                fill="#4E87FF"/>
                                                                        </svg>
                                                                    </div>
                                                                </div>
                                                            @endif
                                                            <!-- End Icon âm thanh -->

                                                            @if(($component->value['img'] === null || $component->value['img'] === "") && isset($component->value['text_vi']) && $component->value['text_vi'] !== "" && $component->value['text_vi'] !== null)
                                                                <div class="justify-self-start font-gen-jyuu-gothic text-[16px] text-[#1E1E1E] w-full">
                                                                    <div>{{ $component->value['text_vi'] }}</div>
                                                                </div>
                                                                <el-divider></el-divider>
                                                            @endif
                                                            @if ($component->value['img'] !== null && $component->value['img'] !== "")
                                                                <div class="justify-self-center max-h-[260px] mb-[24px]">
                                                                    <img
                                                                            class="h-[90%] w-auto object-cover"
                                                                            alt="/cdn/lesson/default/{{ $component->value['img'] }}"
                                                                            src="{{ file_exists(public_path('upload/lessons/' . $component->value['img'])) ? '/upload/lessons/' . $component->value['img'] : '/cdn/lesson/default/' . $component->value['img'] }}">
                                                                </div>
                                                            @endif

                                                            <!-- Phần điền từ -->
                                                            <div
                                                                class="justify-self-center text-2xl font-bold inline justify-content-center flex-wrap items-baseline leading-[54px]
                                                                @if($component->value['audio'] !== null && $component->value['audio'] !== '' && ($component->value['img'] === null || $component->value['img'] === "") && (!isset($component->value['text_vi']) || (isset($component->value['text_vi']) && ($component->value['text_vi'] === "" || $component->value['text_vi'] === null))) )
                                                                    row-span-2
                                                                @endif
                                                            ">
                                                                @foreach ($component->value['question'] as $keyQuestion => $question)
                                                                    @if ($question['type'] === 'default')
                                                                        <div class="my-2 ml-[6px] missing-word-wrap">
                                                                            {!! $question['value'] !!}
                                                                        </div>
                                                                    @else
                                                                        <span class="ml-[6px] my-2">
                                                                <!-- Input field -->
                                                                <input type="text"
                                                                       :disabled="this.disableInput"
                                                                       autocomplete="off"
                                                                       id="missingWord-{{ $keyQuestion }}-{{ $component->id }}"
                                                                       data-id="{{ $component->id }}"
                                                                       data-index="{{ $keyQuestion }}"
                                                                       data-result="{{ $question['result'] }}"
                                                                       class="missingWord missingWord-{{ $component->id }} outline-none min-w-[73px] rounded-[12px] bg-[#D9D9D9] text-center h-12 font-bold text-[24px]"
                                                                       @input="checkInput">
                                                            </span>
                                                                    @endif
                                                                @endforeach
                                                            </div>
                                                            <!-- End Phần điền từ -->
                                                        </div>
                                                    @elseif($component->type === \App\Http\Models\LessonToTask::TYPE_WORD_PAIR_MATCHING)
                                                        <div
                                                            class="flex items-center justify-center mx-auto w-12 h-12 mb-[30px] rounded-full bg-white cursor-pointer shadow-[0px_0px_1px_0px_#4C5D704D] hover:shadow-[0px_2px_4px_0px_#4C5D703D] transition-all"
                                                            @click="clearPairs()"
                                                        >
                                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M3.91 17.1799C3.72 17.1799 3.53 17.1099 3.38 16.9599C2.01 15.5799 1.25 13.7599 1.25 11.8299C1.25 7.81994 4.5 4.55992 8.5 4.55992L14.57 4.57994L13.48 3.5399C13.18 3.2499 13.17 2.77991 13.46 2.47991C13.75 2.17991 14.22 2.16995 14.52 2.45995L16.96 4.79991C17.18 5.00991 17.25 5.33992 17.14 5.61992C17.03 5.89992 16.75 6.08995 16.44 6.08995L8.48999 6.06993C5.31999 6.06993 2.73999 8.65995 2.73999 11.84C2.73999 13.37 3.33999 14.8199 4.42999 15.9099C4.71999 16.1999 4.71999 16.6799 4.42999 16.9699C4.28999 17.1099 4.1 17.1799 3.91 17.1799Z" fill="#757575"/>
                                                                <path d="M9.99991 21.75C9.80991 21.75 9.62992 21.6799 9.47992 21.54L7.03992 19.2C6.81992 18.99 6.7499 18.66 6.8599 18.38C6.9699 18.1 7.24991 17.9099 7.55991 17.9099L15.5099 17.93C18.6799 17.93 21.2599 15.3399 21.2599 12.1599C21.2599 10.6299 20.6599 9.18 19.5699 8.09C19.2799 7.8 19.2799 7.32 19.5699 7.03C19.8599 6.74 20.3399 6.74 20.6299 7.03C21.9999 8.41 22.7599 10.2299 22.7599 12.1599C22.7599 16.1699 19.5099 19.43 15.5099 19.43L9.43991 19.4099L10.5299 20.45C10.8299 20.74 10.8399 21.21 10.5499 21.51C10.3899 21.67 10.1999 21.75 9.99991 21.75Z" fill="#757575"/>
                                                            </svg>
                                                        </div>
                                                        <div class="w-full justify-center flex gap-[25px] pb-[49px]">
                                                            <div
                                                                class="hidden
                                                                border-[#CFF7FF] border-[#FFF8C7] border-[#E2E3FC] border-[#FFDFC9] border-[#FCDAFF] border-transparent
                                                                hover:border-[#CFF7FF] hover:border-[#FFF8C7] hover:border-[#E2E3FC] hover:border-[#FFDFC9] hover:border-[#FCDAFF] hover:border-transparent
                                                                bg-[#CFF7FF] bg-[#FFF8C7] bg-[#E2E3FC] bg-[#FFDFC9] bg-[#FCDAFF]
                                                                "></div>

                                                            <div v-if="currentQuestionPairs && currentQuestionPairs.left && currentQuestionPairs.right" class="flex flex-col gap-[20px]">
                                                                <div v-for="(block, idx) in currentQuestionPairs.left" :key="`pair-block-${block.uuid}`" class="flex items-stretch justify-between gap-[20px]">
                                                                    <div
                                                                        class="w-[300px] rounded-[20px] bg-white min-h-[70px] flex items-center justify-center cursor-pointer transition border-[6px] p-4"
                                                                        :class="[
                                                                            getLeftPairIndex(block.uuid, pairCounter, idCurrentAnswer) !== -1 ? `bg-[${getLeftPairColor(block.uuid, pairCounter, idCurrentAnswer)}]` : '',
                                                                            ['next', 'end'].includes(statusAnswer) ? (checkPairAnswer(block.uuid, 'left', idCurrentAnswer) ? 'border-[#57D061] hover:border-[#57D061]' : 'border-[#FF7C79] hover:border-[#FF7C79]' ) : (getLeftPairIndex(block.uuid, pairCounter, idCurrentAnswer) === -1 ? `border-transparent hover:border-[${pairColors[currentPairIndex]}]` : `border-transparent border-[${getLeftPairColor(block.uuid, pairCounter, idCurrentAnswer)}]`)
                                                                        ]"
                                                                        @click="setPairLeft(block.uuid, pairCounter, idCurrentAnswer, currentQuestionPairs.left.length)"
                                                                    >
                                                                        <span v-html="block.value" class="font-beanbag-regular text-[16px] text-[#1E1E1E] break-words" style="word-break: break-word"></span>
                                                                    </div>
                                                                    <div
                                                                        class="w-[300px] rounded-[20px] bg-white min-h-[70px] flex items-center justify-center cursor-pointer transition border-[6px] p-4"
                                                                        :class="[
                                                                            getRightPairIndex(currentQuestionPairs.right[idx].uuid, pairCounter, idCurrentAnswer) !== -1 ? `bg-[${getRightPairColor(currentQuestionPairs.right[idx].uuid, pairCounter, idCurrentAnswer)}]` : '',
                                                                            ['next', 'end'].includes(statusAnswer) ? (checkPairAnswer(currentQuestionPairs.right[idx].uuid, 'right', idCurrentAnswer) ? 'border-[#57D061] hover:border-[#57D061]' : 'border-[#FF7C79] hover:border-[#FF7C79]' ) : (getRightPairIndex(currentQuestionPairs.right[idx].uuid, pairCounter, idCurrentAnswer) === -1 ? `border-transparent hover:border-[${pairColors[currentPairIndex]}]` : `border-transparent border-[${getRightPairColor(currentQuestionPairs.right[idx].uuid, pairCounter, idCurrentAnswer)}]`)
                                                                        ]"
                                                                        @click="setPairRight(currentQuestionPairs.right[idx].uuid, pairCounter, idCurrentAnswer, currentQuestionPairs.right.length)"
                                                                    >
                                                                        <span v-html="currentQuestionPairs.right[idx].value" class="font-beanbag-regular text-[16px] text-[#1E1E1E] break-words" style="word-break: break-word"></span>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>
                                                    @elseif($component->type === \App\Http\Models\LessonToTask::TYPE_SENTENCE_JUMBLE) <!-- Câu sắp xếp -->
                                                        <div class="grid items-stretch justify-items-stretch align-items-center w-full max-w-[80%] min-h-[250px] bg-white rounded-3xl shadow-lg p-6 mx-auto mb-20">
                                                            <div class="content-question">

{{--                                                                sắp xếp câu--}}
                                                                <!-- Icon âm thanh có ảnh hoặc text-->
                                                                @if($component->value['audio'] !== null && $component->value['audio'] !== '' && (($component->value['title_question'] !== "" && $component->value['title_question'] !== null) || ($component->value['img'] !== null && $component->value['img'] !== "")))
                                                                    <div class="top-[18px] left-[21px] justify-self-start row-start-1 self-start 123123123123123 mb-7">
                                                                        <div class="flex items-center justify-center justify-items-center border-2 border-[#4E87FF] rounded-full py-1 px-2"
                                                                             :class="[currentPlaying && currentPlaying.id === @json($component->id) ? 'animate-blink-small' : 'shadow-[0_0_15px_4px] shadow-[#4E87FF80]']"
                                                                        >
                                                                            <svg
                                                                                    @click="toggleMp3(@json($component->id), 'question')"
                                                                                    class="cursor-pointer"
                                                                                    width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                                <g clip-path="url(#clip0_4414_5169)">
                                                                                    <rect width="34" height="34" rx="17" fill="white"/>
                                                                                    <path d="M25.5004 23.7292C25.2737 23.7292 25.0612 23.6584 24.8629 23.5167C24.3954 23.1626 24.2962 22.4967 24.6504 22.0292C26.8746 19.0684 26.8746 14.9317 24.6504 11.9709C24.2962 11.5034 24.3954 10.8376 24.8629 10.4834C25.3304 10.1292 25.9962 10.2284 26.3504 10.6959C29.1271 14.4076 29.1271 19.5926 26.3504 23.3042C26.1379 23.5876 25.8262 23.7292 25.5004 23.7292Z"
                                                                                          fill="#4E87FF"
                                                                                          :class="{ 'animate-opacity': currentPlaying && currentPlaying.id === @json($component->id) }"
                                                                                    />
                                                                                    <path d="M28.0922 27.2715C27.8655 27.2715 27.653 27.2007 27.4547 27.059C26.9872 26.7049 26.888 26.039 27.2422 25.5715C31.0247 20.5282 31.0247 13.4732 27.2422 8.42988C26.888 7.96238 26.9872 7.29655 27.4547 6.94238C27.9222 6.58821 28.588 6.68738 28.9422 7.15488C33.2914 12.949 33.2914 21.0524 28.9422 26.8465C28.7439 27.1299 28.418 27.2715 28.0922 27.2715Z"
                                                                                          fill="#4E87FF"
                                                                                          :class="{ 'animate-opacity-second': currentPlaying && currentPlaying.id === @json($component->id) }"
                                                                                    />
                                                                                    <path opacity="0.4" d="M22.3118 10.4969V23.5019C22.3118 25.9386 21.4335 27.7661 19.861 28.6444C19.2235 28.9986 18.5152 29.1686 17.7785 29.1686C16.6452 29.1686 15.4268 28.7861 14.1802 28.0069L10.0435 25.4144C9.76018 25.2444 9.43435 25.1453 9.10852 25.1453H7.79102V8.85361H9.10852C9.43435 8.85361 9.76018 8.75445 10.0435 8.58445L14.1802 5.99195C16.2485 4.70278 18.2743 4.47611 19.861 5.35445C21.4335 6.23278 22.3118 8.06028 22.3118 10.4969Z"
                                                                                          fill="#4E87FF"/>
                                                                                    <path d="M7.79232 8.85449V25.1462H7.08398C3.65565 25.1462 1.77148 23.262 1.77148 19.8337V14.167C1.77148 10.7387 3.65565 8.85449 7.08398 8.85449H7.79232Z" fill="#4E87FF"/>
                                                                                </g>
                                                                                <defs>
                                                                                    <clipPath id="clip0_4414_5169">
                                                                                        <rect width="34" height="34" rx="17" fill="white"/>
                                                                                    </clipPath>
                                                                                </defs>
                                                                            </svg>
                                                                            <div class="ml-2 text-[#4E87FF] text-xs font-averta-regular text-blue-500">
                                                                                @{{ formattedTime }} / @{{ timeDurationCurrentAnswer }}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                @endif

                                                                <!-- chỉ có Icon âm thanh-->
                                                                @if($component->value['audio'] !== null && $component->value['audio'] !== '' && (($component->value['title_question'] === "" || $component->value['title_question'] === null) && ($component->value['img'] === null || $component->value['img'] === "")))
                                                                    <div class="items-center flex my-10 justify-center">
                                                                        <button data-audioButton="audioButton-{{ $component->id }}"
                                                                                ref="audioButton-{{ $component->id }}"
                                                                                @click="toggleMp3(@json($component->id), 'question')"
                                                                                :class="['flex items-center justify-center w-[100px] h-[100px] rounded-full bg-white text-blue-500 hover:bg-blue-100 flex-col', (playingAnswerId === @json($component->id) && isPlaying) ? 'animate-blink' : 'shadow-[0_0_15px_4px] shadow-[#4E87FF80]']">
                                                                            <!-- Icon loa -->
                                                                            <div
                                                                                    class="w-[50px] h-[50px] bg-blue-500 rounded-full flex items-center justify-center">
                                                                                <svg width="100px" height="100px" viewBox="0 0 45 44"
                                                                                     fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                                    <path
                                                                                            d="M33.7505 30.7083C33.4505 30.7083 33.1693 30.6166 32.9068 30.4333C32.288 29.975 32.1568 29.1133 32.6255 28.5083C35.5693 24.6766 35.5693 19.3233 32.6255 15.4916C32.1568 14.8866 32.288 14.025 32.9068 13.5666C33.5255 13.1083 34.4068 13.2366 34.8755 13.8416C38.5505 18.645 38.5505 25.355 34.8755 30.1583C34.5943 30.525 34.1818 30.7083 33.7505 30.7083Z"
                                                                                            fill="#4E87FF" fill-opacity="0.5"
                                                                                            :class="{ 'animate-opacity': (currentPlaying && currentPlaying.id === @json($component->id)) }"/>
                                                                                    <path
                                                                                            d="M37.1822 35.2917C36.8822 35.2917 36.6009 35.2001 36.3384 35.0167C35.7197 34.5584 35.5884 33.6967 36.0572 33.0917C41.0634 26.5651 41.0634 17.4351 36.0572 10.9084C35.5884 10.3034 35.7197 9.44172 36.3384 8.98339C36.9572 8.52505 37.8384 8.65339 38.3072 9.25839C44.0634 16.7567 44.0634 27.2434 38.3072 34.7417C38.0447 35.1084 37.6134 35.2917 37.1822 35.2917Z"
                                                                                            fill="#4E87FF" fill-opacity="0.5"
                                                                                            :class="{ 'animate-opacity-second': (currentPlaying && currentPlaying.id === @json($component->id)) }"/>
                                                                                    <path opacity="0.4"
                                                                                          d="M29.5312 13.585V30.415C29.5312 33.5683 28.3688 35.9333 26.2875 37.07C25.4438 37.5283 24.5063 37.7483 23.5312 37.7483C22.0312 37.7483 20.4188 37.2533 18.7688 36.245L13.2938 32.89C12.9188 32.67 12.4875 32.5417 12.0562 32.5417H10.3125V11.4583H12.0562C12.4875 11.4583 12.9188 11.33 13.2938 11.11L18.7688 7.75499C21.5063 6.08666 24.1875 5.79333 26.2875 6.92999C28.3688 8.06666 29.5312 10.4317 29.5312 13.585Z"
                                                                                          fill="#4E87FF"/>
                                                                                    <path
                                                                                            d="M10.3125 11.4583V32.5417H9.375C4.8375 32.5417 2.34375 30.1033 2.34375 25.6667V18.3333C2.34375 13.8967 4.8375 11.4583 9.375 11.4583H10.3125Z"
                                                                                            fill="#4E87FF"/>
                                                                                </svg>
                                                                            </div>
                                                                            <!-- Thời gian -->
                                                                            <span
                                                                                    class="text-[#4E87FF] text-xs font-averta-regular text-blue-500">@{{ formattedTime }} / @{{ timeDurationCurrentAnswer }}</span>
                                                                        </button>
                                                                    </div>
                                                                @endif

                                                                <!-- End Icon âm thanh -->

                                                                @if ($component->value['img'] !== null && $component->value['img'] !== "")
                                                                    <div class="justify-self-center max-h-[260px] mb-[24px]">
                                                                        <img
                                                                                class="h-[90%] w-auto object-cover"
                                                                                alt="/cdn/lesson/default/{{ $component->value['img'] }}"
                                                                                src="{{ file_exists(public_path('upload/lessons/' . $component->value['img'])) ? '/upload/lessons/' . $component->value['img'] : '/cdn/lesson/default/' . $component->value['img'] }}">
                                                                    </div>
                                                                @endif
                                                                @if(isset($component->value['title_question']) && $component->value['title_question'] !== "" && $component->value['title_question'] !== null)
                                                                    <div class="justify-self-start font-gen-jyuu-gothic text-[16px] text-[#1E1E1E] w-full text-center mt-5"
                                                                         v-html="dataLesson.component[@json($key)].value.title_question"
                                                                    >
                                                                    </div>
                                                                @endif
                                                            </div>
                                                            <div class="divider div-transparent div-arrow-down my-5"></div>
                                                            <div class="wrapper-user-result flex justify-center flex-wrap" data-id="{{ $component->id }}">
                                                                <p
                                                                        v-for="item in arrUserResultSentenceJumble"
                                                                        :data-uuid="item"
                                                                        @click="removeWordSentenceJumble(item)"
                                                                        class="sentence-jumble-item-result bg-[#F5F5F5] text-black border-[3px] rounded-[12px] m-2 p-3 font-gen-jyuu-gothic font-medium text-2xl cursor-pointer select-none"
                                                                        v-html="renderSelectWordSentenceJumble(item)">
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <div class="flex justify-center relative flex-wrap">
                                                            <div v-if="showTooltip === 1"  class="absolute arrow_box top-[-60px] left-[44%] bg-[#D9D9D9] rounded-full p-2 text-sm text-[#757575] font-averta-regular opacity-100 transition-opacity duration-500">Chọn để sắp xếp</div>

                                                            @foreach($component->value['items'] as $item)
                                                                <div data-uuid="{{ $item['uuid'] }}"
                                                                     class="sentence-jumble-item bg-[#F5F5F5] text-black border-[3px] rounded-[12px] m-2 p-3 font-gen-jyuu-gothic font-medium text-2xl cursor-pointer"
                                                                     @click="selectWordSentenceJumble(`{{ $item['uuid'] }}`)">
                                                                    {!! $item['value'] !!}
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    @elseif($component->type === \App\Http\Models\LessonToTask::TYPE_SPEAKING) <!-- Câu luyện nói -->
                                                        <div class="grid items-stretch justify-items-stretch align-items-center w-full max-w-[80%] min-h-[250px] bg-white rounded-3xl shadow-lg p-6 mx-auto mb-20">
                                                            <div class="content-question">
                                                                @if ($component->value['img'] !== null && $component->value['img'] !== "")
                                                                    <div class="justify-self-center max-h-[260px] mb-[24px]">
                                                                        <img
                                                                                class="h-[90%] w-auto object-cover"
                                                                                alt="/cdn/lesson/default/{{ $component->value['img'] }}"
                                                                                src="{{ file_exists(public_path('upload/lessons/' . $component->value['img'])) ? '/upload/lessons/' . $component->value['img'] : '/cdn/lesson/default/' . $component->value['img'] }}">
                                                                    </div>
                                                                @endif
                                                                <div class="rounded-2xl border-[#E1EBFF] border-2">
                                                                    <div class="flex justify-center ">
                                                                        <div class="w-1/2">
                                                                            <div
                                                                                    :class="[
                                                                                (currentPlaying && currentPlaying.componentData === 'wavesurferDefaultTypeSpeaking') || !currentPlaying ? 'font-averta-bold' : 'font-averta-regular',
                                                                                statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'wavesurferDefaultTypeSpeaking') ? 'text-[#B3B3B3]' : 'text-[#4E87FF]'
                                                                                ]"
                                                                                    class="text-base text-center  py-3"
                                                                            >
                                                                                <button
                                                                                        :disabled="(currentPlaying && currentPlaying.componentData !== 'wavesurferDefaultTypeSpeaking' && currentPlaying.componentData !== 'wavesurferSlowTypeSpeaking') || statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic'"
                                                                                        @click="toggleMp3(@json($component->id), 'waveQuestion', 'wavesurferDefaultTypeSpeaking')"
                                                                                >
                                                                                    Tiêu chuẩn 1x
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                        <div class="w-1/2">
                                                                            <div
                                                                                    :class="[
                                                                                (currentPlaying && currentPlaying.componentData === 'wavesurferSlowTypeSpeaking') ? 'font-averta-bold' : 'font-averta-regular',
                                                                                statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic' || currentPlaying === null || (currentPlaying && currentPlaying.componentData !== 'wavesurferSlowTypeSpeaking') ? 'text-[#B3B3B3]' : 'text-[#4E87FF]'
                                                                                ]"
                                                                                    class=" text-base text-center py-3">
                                                                                <button
                                                                                        :disabled="(currentPlaying && currentPlaying.componentData !== 'wavesurferDefaultTypeSpeaking' && currentPlaying.componentData !== 'wavesurferSlowTypeSpeaking') || statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic'"
                                                                                        @click="toggleMp3(@json($component->id), 'waveQuestion', 'wavesurferSlowTypeSpeaking')"
                                                                                >
                                                                                    Chậm 0.75x
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="flex items-center rounded-t-2xl p-3 bg-[#E1EBFF] gap-2 w-full">
                                                                        <!-- Nút Play/Pause -->
                                                                        <div style="width: 20px">
                                                                            <button
                                                                                    :disabled="(currentPlaying && currentPlaying.componentData !== 'wavesurferDefaultTypeSpeaking' && currentPlaying.componentData !== 'wavesurferSlowTypeSpeaking') || statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic'"
                                                                                    @click="toggleMp3(@json($component->id), 'waveQuestion', 'wavesurferDefaultTypeSpeaking')"
                                                                                    class="text-blue-600"
                                                                            >
                                                                                    <span v-if="currentPlaying && (currentPlaying.componentData === 'wavesurferDefaultTypeSpeaking' || currentPlaying.componentData === 'wavesurferSlowTypeSpeaking')">
                                                                                                       <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                                            <path d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                                                                                                  :fill="`${statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic' ? '#B3B3B3' : '#4E87FF'}`"/>
                                                                                            <path d="M20.9996 19.11V4.89C20.9996 3.54 20.4296 3 18.9896 3H15.3596C13.9296 3 13.3496 3.54 13.3496 4.89V19.11C13.3496 20.46 13.9196 21 15.3596 21H18.9896C20.4296 21 20.9996 20.46 20.9996 19.11Z"
                                                                                                  :fill="`${statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic' ? '#B3B3B3' : '#4E87FF'}`"/>
                                                                                        </svg>
                                                                                    </span>
                                                                                <span v-else>
                                                                                        <svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                                            <path d="M15 7.26795C16.3333 8.03775 16.3333 9.96225 15 10.7321L3 17.6603C1.66667 18.4301 1.01267e-06 17.4678 1.07997e-06 15.9282L1.68565e-06 2.0718C1.75295e-06 0.532196 1.66667 -0.430054 3 0.339746L15 7.26795Z"
                                                                                                  :fill="`${(currentPlaying && currentPlaying.componentData !== 'wavesurferDefaultTypeSpeaking' && currentPlaying.componentData !== 'wavesurferSlowTypeSpeaking') || statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic' ? '#B3B3B3' : '#4E87FF'}`"/>
                                                                                        </svg>
                                                                                    </span>
                                                                            </button>
                                                                        </div>

                                                                        <!-- Waveform -->
                                                                        <div
                                                                                v-show="(currentPlaying && currentPlaying.componentData !== 'wavesurferSlowTypeSpeaking') || !currentPlaying"
                                                                                ref="{{ $component->id }}-default-waveform"
                                                                                class="{{ $component->id }}-default-waveform flex-1 h-10 w-full"
                                                                        ></div>

                                                                        <!-- Waveform -->
                                                                        <div
                                                                                v-show="(currentPlaying && currentPlaying.componentData === 'wavesurferSlowTypeSpeaking')"
                                                                                ref="{{ $component->id }}-slow-waveform"
                                                                                class="flex-1 h-10 w-full"></div>

                                                                        <!-- Thời gian -->
                                                                        <div class="text-base font-averta-regular w-[105px] flex justify-center"
                                                                             :style="{
                                                                                    'color': (currentPlaying && currentPlaying.componentData !== 'wavesurferDefaultTypeSpeaking' && currentPlaying.componentData !== 'wavesurferSlowTypeSpeaking') || statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic' ? '#B3B3B3' : '#4E87FF'
                                                                                }"
                                                                        >
                                                                            @{{ currentTimeTypeSpeaking }} / @{{ durationTypeSpeaking }}
                                                                        </div>

                                                                        <!-- Nút âm lượng -->
                                                                        <button class="text-blue-600">
                                                                            <img
                                                                                    v-if="(currentPlaying && currentPlaying.componentData !== 'wavesurferDefaultTypeSpeaking' && currentPlaying.componentData !== 'wavesurferSlowTypeSpeaking') || statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic'"
                                                                                    src="{{ asset("/images/lessons/svg/icon-audio-small-disable.svg") }}">
                                                                            <img v-else src="{{ asset("/images/lessons/svg/icon-audio-small.svg") }}">
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="flex mb-5">
                                                            <div class="speak-slow w-1/2 mr-2">
                                                                <div class="flex items-center">
                                                                    <div class="rounded-[50%] w-[40px] h-[40px] justify-center justify-items-center flex items-center text-2xl mr-1"
                                                                         :style="{ 'background': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_SLOW || statusSpeak.recordDefaultStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordSlow') ? '#D9D9D9' : '#CCF8D1' }"
                                                                    >1</div>
                                                                    <div class="max-w-full overflow-hidden"
                                                                         :style="{
                                                                             'color': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_SLOW || statusSpeak.recordDefaultStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordSlow') ? '#757575' : '#07403F',
                                                                             'font-size': 'clamp(12px, 2.5vw, 24px)'
                                                                           }"
                                                                         :class="`${statusSpeak.stepViewSpeaking == stepTypeSpeak.RECORD_SLOW || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData === 'recordSlow') ? 'font-averta-bold' : 'font-averta-regular'}`">
                                                                        <span class="inline-block whitespace-nowrap">Luyện nói tốc độ chậm</span>
                                                                    </div>
                                                                    <div
                                                                            v-show="statusSpeak.recordSlowStatus == 'recorded'"
                                                                            :class="`${statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_SLOW || statusSpeak.recordDefaultStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordSlow') ? 'opacity-50' : ''} ml-2`"
                                                                    >
                                                                        <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                            <path d="M18 2L7 13L2 8" stroke="#07403F" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                                                                        </svg>
                                                                    </div>
                                                                </div>
                                                                <div id="runText-recordSlow-{{ $component->id }}"
                                                                     class="run-text runText-{{ $component->id }} runText-recordSlow-{{ $component->id }}  font-gen-jyuu-gothic text-2xl mt-6" data-text="{{ html_entity_decode(strip_tags($component->value['title_question'])) }}"
                                                                     :class="statusSpeak.recordSlowStatus == 'mic' ? 'text-black' : 'text-[#757575]'"
                                                                >
{{--                                                                    {!! $component->value['title_question'] !!}--}}
                                                                </div>
                                                                <div class="record-wrap flex mt-3">
                                                                    <div class="record-content w-2/3 rounded-full flex items-center p-2"
                                                                         :style="{
                                                                        'background': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_SLOW || statusSpeak.recordDefaultStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordSlow') ? '#E6E6E6' : (statusSpeak.recordSlowStatus == 'mic' ? '#FFB98F' : '#CCF8D1')
                                                                        }"
                                                                    >
                                                                        <div class="w-[20px] btn-toggle-play">
                                                                            <button class="cursor-pointer"
                                                                                    v-if="statusSpeak.recordSlowStatus == 'recorded'"
                                                                                    :disabled="statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordSlow')"
                                                                                    @click="toggleMp3(@json($component->id), 'record', 'recordSlow')"
                                                                            >
                                                                                <img v-if="currentPlaying && currentPlaying.componentData === 'recordSlow'" src="{{ asset('images/lessons/svg/pause-record.svg') }}">
                                                                                <img v-else src="{{ asset('images/lessons/svg/play-record.svg') }}">
                                                                            </button>
                                                                        </div>
                                                                        <div class=" w-3/4 px-2 wave-surfer-record">
                                                                            <div v-show="!recordSlow" ref="mic-{{ $component->id }}-recordSlowMic" class="mic-{{ $component->id }}-recordSlowMic record-audio-wrap-mic slow-record-mic-wrap dot-container"></div>
                                                                            <div v-show="recordSlow" ref="mic-{{ $component->id }}-recordSlow" class="record-audio-wrap slow-record-wrap"></div>
                                                                        </div>
                                                                        <div class="self-center recordSlowTime rounded-full font-averta-regular text-xs ml-2 px-2"
                                                                             :style="{
                                                                                'background': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_SLOW || statusSpeak.recordDefaultStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordSlow') ? '#F5F5F5' : (statusSpeak.recordSlowStatus == 'mic' ? '#FFF4E0' : '#F0FFF1' ),
                                                                                'color': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_SLOW || statusSpeak.recordDefaultStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordSlow') ? '#757575' : (statusSpeak.recordSlowStatus == 'mic' ? '#EF6D13' : '#07403F' )
                                                                            }"
                                                                        >
                                                                            00:00
                                                                        </div>
                                                                    </div>
                                                                    <div class="record-btn ml-3 w-[80px]">
                                                                        <button
                                                                                @click="runRecord('recordSlow', 'click')" class="text-blue-600 p-2 rounded-full min-h-[44px] w-full"
                                                                                :disabled="statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_SLOW || statusSpeak.recordDefaultStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordSlow')"
                                                                                :style="{
                                                                                    'background': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_SLOW || statusSpeak.recordDefaultStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordSlow') ? '#E6E6E6' : (statusSpeak.recordSlowStatus == 'mic' ? '#FFB98F' : '#CCF8D1')
                                                                                }"
                                                                        >
                                                                            <span v-if="statusSpeak.recordSlowStatus === null">
                                                                                <img src="{{ asset("/images/lessons/svg/microphone-start.svg") }}">
                                                                            </span>
                                                                            <span v-if="statusSpeak.recordSlowStatus === 'mic'">
                                                                                <img src="{{ asset("/images/lessons/svg/microphone-pause.svg") }}">
                                                                            </span>
                                                                            <span v-if="statusSpeak.recordSlowStatus === 'recorded'">
                                                                                <img src="{{ asset("/images/lessons/svg/repeat.svg") }}">
                                                                            </span>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>







                                                            <div class="speak-default w-1/2 ml-2">
                                                                <div class="flex items-center">
                                                                    <div class="rounded-[50%] w-[40px] h-[40px] justify-center justify-items-center flex items-center text-2xl mr-1"
                                                                         :style="{ 'background': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_DEFAULT || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordDefault') ? '#D9D9D9' : '#CCF8D1' }">
                                                                        2
                                                                    </div>
                                                                    <div class="max-w-full overflow-hidden"
                                                                         :style="{
                                                                             'color': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_DEFAULT || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordDefault') ? '#757575' : '#07403F',
                                                                             'font-size': 'clamp(12px, 2.5vw, 24px)'
                                                                           }"
                                                                         :class="`${statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.stepViewSpeaking == stepTypeSpeak.RECORD_DEFAULT ? 'font-averta-bold' : 'font-averta-regular'}`">
                                                                        <span class="inline-block whitespace-nowrap">Luyện nói tốc độ tiêu chuẩn</span>

                                                                    </div>
                                                                    <div v-show="statusSpeak.recordDefaultStatus == 'recorded'"
                                                                         :class="`${statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_DEFAULT || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordDefault') ? 'opacity-50' : ''} ml-2`">
                                                                        <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                            <path d="M18 2L7 13L2 8" stroke="#07403F" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                                                                        </svg>
                                                                    </div>
                                                                </div>
                                                                <div id="runText-recordDefault-{{ $component->id }}"
                                                                     class="run-text runText-{{ $component->id }} runText-recordDefault-{{ $component->id }} font-gen-jyuu-gothic text-2xl mt-6" data-text="{{ html_entity_decode(strip_tags($component->value['title_question'])) }}"
                                                                     :class="statusSpeak.recordDefaultStatus == 'mic' ? 'text-black' : 'text-[#757575]'"
                                                                >
                                                                </div>
                                                                <div class="record-wrap flex mt-3">
                                                                    <div class="record-content w-2/3 rounded-full flex items-center p-2"
                                                                        :style="{
                                                                        'background': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_DEFAULT || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordDefault') ? '#E6E6E6' : (statusSpeak.recordDefaultStatus == 'mic' ? '#FFB98F' : '#CCF8D1')
                                                                        }"
                                                                    >
                                                                        <div class="w-[20px] btn-toggle-play">
                                                                            <button class="cursor-pointer"
                                                                                    v-if="statusSpeak.recordDefaultStatus == 'recorded'"
                                                                                    :disabled="statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordDefault')"
                                                                                    @click="toggleMp3(@json($component->id), 'record', 'recordDefault')"
                                                                            >
                                                                                <img v-if="currentPlaying && currentPlaying.componentData === 'recordDefault'" src="{{ asset('images/lessons/svg/pause-record.svg') }}">
                                                                                <img v-else src="{{ asset('images/lessons/svg/play-record.svg') }}">
                                                                            </button>
                                                                        </div>
                                                                        <div class=" w-3/4 px-2 wave-surfer-record">
                                                                            <div v-show="!recordDefault" ref="mic-{{ $component->id }}-recordDefaultMic" class="mic-{{ $component->id }}-recordDefaultMic record-audio-wrap-mic default-record-mic-wrap dot-container"></div>
                                                                            <div v-show="recordDefault" ref="mic-{{ $component->id }}-recordDefault" class="record-audio-wrap default-record-wrap"></div>
                                                                        </div>
                                                                        <div class="self-center recordDefaultTime rounded-full font-averta-regular text-xs ml-2 px-2"
                                                                            :style="{
                                                                                'background': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_DEFAULT || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordDefault') ? '#F5F5F5' : (statusSpeak.recordDefaultStatus == 'mic' ? '#FFF4E0' : '#F0FFF1' ),
                                                                                'color': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_DEFAULT || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordDefault') ? '#757575' : (statusSpeak.recordDefaultStatus == 'mic' ? '#EF6D13' : '#07403F' )

                                                                            }"
                                                                        >
                                                                            00:00
                                                                        </div>
                                                                    </div>
                                                                    <div class="record-btn ml-3 w-[80px]">
                                                                        <button
                                                                                @click="runRecord('recordDefault', 'click')" class="text-blue-600 p-2 rounded-full min-h-[44px] w-full"
                                                                                :disabled="(statusSpeak.recordSlowStatus == 'mic' || statusSpeak.stepViewSpeaking <= stepTypeSpeak.RECORD_SLOW)"
                                                                                :style="{
                                                                                    'background': statusSpeak.stepViewSpeaking < stepTypeSpeak.RECORD_DEFAULT || statusSpeak.recordSlowStatus == 'mic' || (currentPlaying && currentPlaying.componentData !== 'recordDefault') ? '#E6E6E6' : (statusSpeak.recordDefaultStatus == 'mic' ? '#FFB98F' : '#CCF8D1')
                                                                                }"
                                                                        >
                                                                            <span v-if="statusSpeak.recordDefaultStatus === null">
                                                                                <img src="{{ asset("/images/lessons/svg/microphone-start.svg") }}">
                                                                            </span>
                                                                            <span v-if="statusSpeak.recordDefaultStatus === 'mic'">
                                                                                <img src="{{ asset("/images/lessons/svg/microphone-pause.svg") }}">
                                                                            </span>
                                                                            <span v-if="statusSpeak.recordDefaultStatus === 'recorded'">
                                                                                <img src="{{ asset("/images/lessons/svg/repeat.svg") }}">
                                                                            </span>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                </div>
                                            @endforeach
                                            <div class="record-noti flex justify-center mt-15">
                                                <div class="noti-auto-next-step hidden text-[#EF6D13] text-2xl font-averta-regular">
                                                    Ghi âm sẽ bắt đầu sau <span class="time-auto-next-step font-averta-bold text-bold">3s</span>
                                                </div>
                                                <div class="noti-remind font-averta-regular text-[#757575] text-xl text-center max-w-2xl hidden">
                                                    *Kiểm tra lại micro trên thiết bị bạn đang sử dụng nếu bạn không ghi âm được nha!
                                                </div>
                                                <div class="noti-done-record font-averta-regular text-[#757575] text-xl text-center max-w-2xl hidden">
                                                    *Nghe phát âm mẫu và lặp lại theo nhiều lần để cải thiện phát âm, ngữ điệu.
                                                </div>
                                            </div>
                                            <div class="pulse-container hidden z-0" ref="pulseContainer">
                                                <div ref="pulseCircle" class="pulse-circle"></div>
                                            </div>
{{--                                            <div class="record-blur flex items-center justify-center">--}}
{{--                                                <div class="w-[500px] rouned-full" :class="isPlayingTypeSpeaking ? 'animate-record-blur' : 'shadow-[0_0_15px_4px] shadow-[#FFB98F]'"></div>--}}
{{--                                            </div>--}}

                                            <!-- Submit Button -->
                                            <div class="mt-8 text-center mb-15 flex max-w-[695px] mx-auto">
                                                <div id="explanationButton"
                                                     v-if="(statusAnswer == 'next' || statusAnswer == 'end') && isShowExplainButton">
                                                    <Transition name="slide-fade">
                                                        {{--                            <button v-if="" id="explanation" @click="showExplanationPopup()" --}}
                                                        {{--                                    class="text-[12px] text-[#57D061] border-[2px] border-[#57D061] bg-white text-bold px-6 py-2 rounded-full mr-2.5 w-[151px] opacity-0 transform -translate-x-10 transition duration-500 ease-in-out"> --}}
                                                        {{--                                GIẢI THÍCH --}}
                                                        {{--                            </button> --}}
                                                        <button id="explanation" @click="showExplanationPopup()"
                                                                class="text-[12px] text-[#57D061] border-[2px] border-[#57D061] bg-white text-bold px-6 py-3 rounded-full mr-2.5 w-[151px]">
                                                            GIẢI THÍCH
                                                        </button>
                                                    </Transition>
                                                </div>

                                                <button
                                                        v-show="currentAnswer.type != 16 || (currentAnswer.type == 16 && recordDefault && recordSlow)"
                                                        id="checkButton" @click="actionCheckButton()" :disabled="statusAnswer == 'doing'"
                                                        :class="`px-6 py-3 rounded-full text-sm w-full translate-x-0 scale-100 transition-all duration-300 ease-in-out ${statusAnswer !== 'doing' ? ' drop-shadow-2xl text-[#07403F] bg-[#57D061]' : ' text-[#B3B3B3] bg-[#D9D9D9]'}`">
                                                    @{{ statusAnswer === 'end' ? 'NỘP BÀI' : (statusAnswer !== 'next' ? 'KIỂM TRA' :
                                                    'LÀM TIẾP') }}
                                                </button>
                                            </div>
                                        </div>
                                    </main>
                                    {{--    {{ dd($contentLesson->toArray()) }} --}}
                                </template>
                                <template v-else>
                                    <main class="mx-auto pt-8 flex-1 font-beanbag bg-[#F4F5FA]">
                                        <div class="rounded-lg p-6">
                                            <!-- Lesson Title -->
                                            <div class="flex justify-content-space-between align-items-center mb-[24px]">
                                                {{--                                {{ dd($contentLesson) }} --}}
                                                <div class="mb-0 font-averta-semibold">
                                                    <div class="icon-require flex">
                                                        @if ($lesson->name_html)
                                                            <h1 class="
                                                            text-[18px] font-bold text-black">{!! $contentLesson->name_html !!}</h1><span>
                                                        @else
                                                                    <h1 class="text-[18px] font-bold text-black">{{ $contentLesson->name }}</h1>
                                                                @endif
                                                                @if($currentLesson['require'])
                                                                    <img
                                                                            class="max-h-[12px] relative ml-[5px]"
                                                                            :src="`/images/icons/require.png`"
                                                                            alt="require.png"
                                                                    />
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-center font-beanbag  text-[#07403F] text-[24px] uppercase">
                                            <div class="text-center mb-[12px]">
                                                <h3 v-if="userResult.isExerciseSpeak">
                                                    HOÀN THÀNH RỒI
                                                </h3>
                                                <h3 v-else>
                                                    @{{ userResult.correct_answer }}/@{{ userResult.total_answer }} CÂU ĐÚNG!
                                                </h3>
                                            </div>
                                            <div class="text-center">
                                                <template v-if="userResult.ratio_correct_answer >= 0.85 || userResult.isExerciseSpeak">
                                                    <h3>Bạn làm tốt lắm!</h3>
                                                </template>
                                                <template v-else>
                                                    <h3>Cố gắng thêm nhé!</h3>
                                                </template>
                                            </div>
                                            <div class="mb-[122px]">
                                                <template v-if="userResult.ratio_correct_answer >= 0.85 || userResult.isExerciseSpeak">
                                                    <img src="/images/lessons/svg/result-pass.svg">
                                                </template>
                                                <template v-else>
                                                    <img src="/images/lessons/svg/result-false.svg">
                                                </template>
                                            </div>
                                            {{--                            <div class="flex items-center justify-items-center justify-content-center min-h-[50px]"> --}}
                                            {{--                                <h4 class="mx-[5px] my-0 text-[#07403F] text-[16px] leading-relaxed">NHẬN ĐƯỢC <span --}}
                                            {{--                                            class="text-[#41A336]">@{{ userResult.correct_answer * 10 }}</span> --}}
                                            {{--                                </h4> --}}

                                            {{--                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" --}}
                                            {{--                                     xmlns="http://www.w3.org/2000/svg" --}}
                                            {{--                                     class="mr-[10px]"> --}}
                                            {{--                                    <path d="M6.83765 18.4944C2.27709 10.8935 8.35783 3.29255 21.2794 4.05264C22.0395 16.9742 14.4386 23.0549 6.83765 18.4944Z" --}}
                                            {{--                                          fill="#96D962" stroke="#41A336" stroke-width="2" stroke-linecap="round" --}}
                                            {{--                                          stroke-linejoin="round"/> --}}
                                            {{--                                    <path d="M16 9.33337L4 21.3334" stroke="#41A336" stroke-width="2" --}}
                                            {{--                                          stroke-linecap="round" --}}
                                            {{--                                          stroke-linejoin="round"/> --}}
                                            {{--                                </svg> --}}
                                            {{--                                <el-tooltip placement="right" class="icon-wrapper" effect="customized"> --}}
                                            {{--                                    <div slot="content">Có thể dùng để đổi<br/>voucher giảm học phí!</div> --}}
                                            {{--                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" --}}
                                            {{--                                         xmlns="http://www.w3.org/2000/svg"> --}}
                                            {{--                                        <circle cx="12" cy="12" r="11" stroke="#D9D9D9" stroke-width="2"/> --}}
                                            {{--                                        <path d="M10.5 7.065C10.5 6.745 10.625 6.485 10.875 6.285C11.105 6.095 11.405 6 11.775 6C12.145 6 12.45 6.095 12.69 6.285C12.93 6.485 13.05 6.745 13.05 7.065V7.095C13.05 7.415 12.93 7.675 12.69 7.875C12.45 8.075 12.145 8.175 11.775 8.175C11.405 8.175 11.105 8.075 10.875 7.875C10.625 7.675 10.5 7.415 10.5 7.095V7.065ZM10.635 10.035C10.635 9.715 10.745 9.445 10.965 9.225C11.195 9.005 11.465 8.895 11.775 8.895C12.095 8.895 12.365 9.005 12.585 9.225C12.805 9.445 12.915 9.715 12.915 10.035V15.975C12.915 16.285 12.805 16.555 12.585 16.785C12.365 17.005 12.095 17.115 11.775 17.115C11.465 17.115 11.195 17.005 10.965 16.785C10.745 16.555 10.635 16.285 10.635 15.975V10.035Z" --}}
                                            {{--                                              fill="#D9D9D9"/> --}}
                                            {{--                                    </svg> --}}
                                            {{--                                </el-tooltip> --}}
                                            {{--                            </div> --}}
                                            {{--                            <div class="max-w-[695px] mx-auto"> --}}
                                            {{--                                <el-divider></el-divider> --}}
                                            {{--                            </div> --}}
                                        </div>
                                        <!-- Submit Button -->
                                        <div class="my-[100px] text-center max-w-[695px] mx-auto grid grid-cols-3 gap-3 items-center">
                                            <button @click="dialogUserResultVisible = true"
                                                    class="link-review-result-lesson text-sm text-[#57D061] uppercase underline hover-text-[#45BD4F] hover:scale-125 transition duration-300 hover:-translate-y-1 ease-in-out">
                                                xem lại bài làm
                                            </button>
                                            <button id="checkButton" @click="reload()"
                                                    :style="{ background: userResult.ratio_correct_answer >= 0.85 ? 'white' : '#FFF193' }"
                                                    :class="`px-6 py-2 rounded-full text-sm w-full translate-x-0 scale-100 transition-all duration-300 ease-in-out text-[#07403F] drop-shadow-2xl uppercase`">
                                                Làm lại
                                            </button>
                                            @if($nextLessonCurrentGroup != null)
                                                <button
                                                    @click="nextLesson($event)"
                                                    href="/khoa-hoc/{{ $course->SEOurl }}/lesson/{{ $nextLessonCurrentGroup->id }}-{{ $nextLessonCurrentGroup->SEOurl }}"
                                                    :class="`px-6 py-2 rounded-full text-sm w-full translate-x-0 scale-100 transition-all duration-300 ease-in-out text-[#07403F] bg-[#57D061] drop-shadow-2xl uppercase`">
                                                    Học tiếp
                                                </button>
                                            @endif
                                        </div>
                                    </main>
                                </template>
                            </div>

                            @if ($isLocked || !auth()->check())
                                <!-- Lớp overlay yêu cầu mua khóa học -->
                                <div id="purchase-overlay"
                                     class="absolute inset-0 flex flex-col items-center justify-center rounded-lg z-10">
                                    <div class="flex items-center mb-[36px]">
                                        <img src="/images/lessons/svg/lock.svg">
                                        <p
                                            class="mb-0 text-[20px] font-semibold text-center font-beanbag text-[#07403F] leading-[2px]">
                                            @if (!auth()->check())
                                                Bạn cần đăng nhập để học hoặc bình luận
                                            @elseif($isLocked)
                                                Mua khóa học để học ngay
                                            @endif
                                        </p>
                                    </div>

                                    @if (!auth()->check())
                                        <div class="flex items-center justify-center font-beanbag">
                                            <a data-fancybox data-animation-duration="300" data-src="#auth-container"
                                               style="margin-right: 10px;">
                                                <div class="font-beanbag text-[20px] bg-[#57D061] mx-5 sp:mx-1 sp:text-base !text-[#07403F] border border-white rounded-full py-2 w-[170px] text-center uppercase cursor-pointer"
                                                     onclick="swichTab('login')">Đăng nhập
                                                </div>
                                            </a>
                                            <a data-fancybox data-animation-duration="300" data-src="#auth-container">
                                                <div class="bg-[#EC6E23] mx-5 sp:mx-1 text-[20px] sp:text-base !text-white border border-white rounded-full py-2 w-[170px] text-center uppercase cursor-pointer"
                                                     onclick="swichTab('register')">Đăng ký
                                                </div>
                                            </a>

                                        </div>
                                    @elseif($isLocked)
                                        <button @click="buyNow(course.id)" href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}"
                                           class="bg-[#EC6E23] text-white text-[17px] font-beanbag px-6 py-2 rounded-full border-white border-[1px]">
                                            MUA NGAY
                                        </button>
                                    @endif
                                </div>
                            @endif
                        </div>

                        <div v-show="windowWidth <= 640" id="screen-mobile" class="text-center flex flex-wrap justify-center">
                            <img src="/images/lessons/exercise-mobile.png" class="object-contain">
                            <a id="downloadApp" :href="checkDevice() === 'ios' ? 'https://apps.apple.com/us/app/id1486123836' : 'https://play.google.com/store/apps/details?id=com.dungmori.dungmoriapp'"
                               class="mt-[80px] font-beanbag text-[20px] text-white bg-[#EF6D13] text-bold px-6 pt-[14px] pb-[11px] rounded-full w-[260px] uppercase drop-shadow-2xl"
                            >
                                tải app ngay
                            </a>
                        </div>
                    </div>
                </div>
            @endif

            <div class="relative font-beanbag">
                <!-- Overlay -->
                <div @click="hideExplanationPopup()" id="popupOverlay" class="fixed inset-0 hidden z-[1]"></div>

                <!-- Popup Giải thích -->
                <div id="explanationPopup"
                     :class="`${isAnswerSuccess ? 'bg-[#F0FFF1] ' : 'bg-[#FEE9E7]'}  z-[2] fixed bottom-0 left-0 right-0 bg-[#F0FFF1] rounded-t-3xl shadow-lg transform translate-y-full transition-transform ease-in-out p-6 hidden duration-300`"
                >
                    <div class="max-w-[685px]  mx-auto">
                        <div class="grid grid-cols-6 gap-4">
                            <!-- Icon -->
                            <div class="col-start-1 col-span-6">
                                <div class="flex justify-content-space-between">
                                    <div class="text-green-500 flex align-items-center">
                                        <div id="iconResultExplanationPopup">
                                            <svg v-if="isAnswerSuccess" width="28" height="28"
                                                 viewBox="0 0 28 28" fill="none"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="14" cy="14" r="14" fill="#57D061"/>
                                                <path d="M19.3337 10L12.0003 17.3333L8.66699 14" stroke="#EBFFEE"
                                                      stroke-width="3" stroke-linecap="round"
                                                      stroke-linejoin="round"/>
                                            </svg>
                                            <svg v-else width="28" height="28" viewBox="0 0 28 28"
                                                 fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="14" cy="14" r="14" fill="#FF7C79"/>
                                                <g clip-path="url(#clip0_87_1918)">
                                                    <path d="M8 20L20 8" stroke="white" stroke-width="3"
                                                          stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M20 20L8 8" stroke="white" stroke-width="3"
                                                          stroke-linecap="round" stroke-linejoin="round"/>
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_87_1918">
                                                        <rect width="16" height="16" fill="white"
                                                              transform="translate(6 6)"/>
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                        </div>

                                        <p id="textResultExplanationPopup"
                                           :class="` ${isAnswerSuccess ? 'text-[#57D061] ' : 'text-[#FF7C79] '} ml-2 text-[#57D061] font-semibold`">
                                            @{{ isAnswerSuccess ? 'Chính xác' : 'Không chính xác' }}
                                        </p>
                                    </div>

                                    <button v-if="isShowExplainIconAudio" class="buttonAudio text-blue-600 focus:outline-none" data-id="audio"
                                            id="buttonAudio">
                                        <svg width="40" height="40" viewBox="0 0 40 40" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <rect x="1" y="1" width="38" height="38" rx="19"
                                                  stroke="#4E87FF" stroke-width="2"/>
                                            <path
                                                    d="M26.0003 24.75C25.8403 24.75 25.6903 24.7 25.5503 24.6C25.2203 24.35 25.1503 23.88 25.4003 23.55C26.9703 21.46 26.9703 18.54 25.4003 16.45C25.1503 16.12 25.2203 15.65 25.5503 15.4C25.8803 15.15 26.3503 15.22 26.6003 15.55C28.5603 18.17 28.5603 21.83 26.6003 24.45C26.4503 24.65 26.2303 24.75 26.0003 24.75Z"
                                                    fill="#4E87FF"/>
                                            <path
                                                    d="M27.8304 27.25C27.6704 27.25 27.5204 27.2 27.3804 27.1C27.0504 26.85 26.9804 26.38 27.2304 26.05C29.9004 22.49 29.9004 17.51 27.2304 13.95C26.9804 13.62 27.0504 13.15 27.3804 12.9C27.7104 12.65 28.1804 12.72 28.4304 13.05C31.5004 17.14 31.5004 22.86 28.4304 26.95C28.2904 27.15 28.0604 27.25 27.8304 27.25Z"
                                                    fill="#4E87FF"/>
                                            <path opacity="0.4"
                                                  d="M23.75 15.41V24.59C23.75 26.31 23.13 27.6 22.02 28.22C21.57 28.47 21.07 28.59 20.55 28.59C19.75 28.59 18.89 28.32 18.01 27.77L15.09 25.94C14.89 25.82 14.66 25.75 14.43 25.75H13.5V14.25H14.43C14.66 14.25 14.89 14.18 15.09 14.06L18.01 12.23C19.47 11.32 20.9 11.16 22.02 11.78C23.13 12.4 23.75 13.69 23.75 15.41Z"
                                                  fill="#4E87FF"/>
                                            <path
                                                    d="M13.5 14.25V25.75H13C10.58 25.75 9.25 24.42 9.25 22V18C9.25 15.58 10.58 14.25 13 14.25H13.5Z"
                                                    fill="#4E87FF"/>
                                        </svg>
                                    </button>
                                    <audio id="audio" src="" autoplay></audio>
                                </div>
                            </div>

                            <div class="col-start-1 col-span-6">
                                <div id="content_explain" class="mt-2 text-lg text-gray-800 font-gen-jyuu-gothic">

                                </div>
                            </div>
                        </div>
                        <button @click="hideExplanationPopup()"
                                class="bg-white text-gray-500 hover:text-gray-700 focus:outline-none absolute top-[14px] right-[15px] rounded-full">
                            <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <g id="Menu / Close_SM">
                                    <path id="Vector" d="M16 16L12 12M12 12L8 8M12 12L16 8M12 12L8 16"
                                          stroke="#000000" stroke-width="2" stroke-linecap="round"
                                          stroke-linejoin="round"/>
                                </g>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="relative">
                <div id="popupOverlayNotAction" class="fixed inset-0 z-[101] bg-[#000000] bg-opacity-50 hidden"></div>
                <div id="notActionPopup"
                     :class="`z-[102] fixed top-[20%] left-0 right-0 transform translate-y-[300%] transition-transform ease-in-out p-6 duration-500 hidden`"
                >
                    <div class="max-w-[685px] mx-auto relative min-h-[100px] max-h-[236px]"
                    >
                        <img src="{{ asset('images/lessons/svg/bg-pause-when-not-action.svg') }}">
                        <div class="absolute bottom-3 right-12">
                            <div class="flex">
                                <button @click="goBack()" class="rounded-full text-[#1E1E1E] w-[170px] text-center px-5 py-2 border-2 border-[#D9D9D9] font-averta-bold mr-2">Thoát</button>
                                <button @click="hideNotActionPopup()" class="rounded-full text-[#1E1E1E] w-[170px] text-center font-averta-bold bg-[#CCF8D1] shadow-lg">Làm tiếp</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="modalDoneProgressGroup" class="modal fade" tabindex="-1" role="dialog" style="padding-right: 0 !important;">
            <div class="modal-dialog" role="document">
                <div class="modal-content rounded-[24px] max-w-[800px]"
                     style='background-image: url("{{ asset('/images/lessons/svg/achievement/bg-popup-done-stage.svg') }}"); box-shadow: none; border-radius: 24px'
                >
                    <div class="modal-body">
                        <div class="text-center">
                            <div class="flex justify-end m-[5px]">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" onclick="$('#modalDoneProgressGroup').modal('hide')" class="cursor-pointer">
                                    <path d="M13.9435 12L23.6437 2.29978C23.8809 2.03805 24.0083 1.69514 23.9996 1.34205C23.9909 0.988965 23.8467 0.652743 23.597 0.402997C23.3473 0.153252 23.011 0.0091107 22.6579 0.000417292C22.3049 -0.00827612 21.962 0.119144 21.7002 0.356296L12 10.0565L2.29978 0.356296C2.03805 0.119144 1.69514 -0.00827612 1.34205 0.000417292C0.988965 0.0091107 0.652743 0.153252 0.402997 0.402997C0.153252 0.652743 0.0091107 0.988965 0.000417292 1.34205C-0.00827612 1.69514 0.119144 2.03805 0.356296 2.29978L10.0565 12L0.356296 21.7002C0.119144 21.962 -0.00827612 22.3049 0.000417292 22.6579C0.0091107 23.011 0.153252 23.3473 0.402997 23.597C0.652743 23.8467 0.988965 23.9909 1.34205 23.9996C1.69514 24.0083 2.03805 23.8809 2.29978 23.6437L12 13.9435L21.7002 23.6437C21.962 23.8809 22.3049 24.0083 22.6579 23.9996C23.011 23.9909 23.3473 23.8467 23.597 23.597C23.8467 23.3473 23.9909 23.011 23.9996 22.6579C24.0083 22.3049 23.8809 21.962 23.6437 21.7002L13.9435 12Z" fill="#212121"/>
                                </svg>
                            </div>
                            <div class="title">
                                <div class="text-[#57D061] font-beanbag text-[20px] leading-1 mb-[21px]">
                                    Chúc mừng bạn đã
                                </div>
                                <div class="text-[#07403F] font-zuume-semibold text-[48px] uppercase leading-[51px] mb-[26px]">
                                    Hoàn thành khóa {{ $course->name }}
                                </div>
                            </div>
                            <div class="max-w-[176px] mx-auto"></div>
                            <div class="text-center mb-[12px]">
                                <img src="{{ asset('/images/lessons/svg/achievement/content-popup-done-stage.svg') }}" class="">
                            </div>
                            @php
                                $urls = [
                                    'so-cap-n5' => ["so-cap-n4", "Học tiếp sơ cấp N4"],
                                    'so-cap-n4' => ["khoa-n3", "Học tiếp khóa N3"],
                                    'luyen-de-n5' => ["so-cap-n4", "Học tiếp sơ cấp N4"],
                                    'luyen-de-n4' => ["khoa-n3", "Học tiếp khóa N3"],
                                ];

                            @endphp
                            <div class="text-center mb-[12px]">
                                <a
                                    href="{{ route('frontend.course.detail', ['url' => $urls[$course->SEOurl][0]]) }}"
                                    class="mx-auto font-beanbag text-white hover:text-white uppercase text-[16px] flex items-center justify-center w-[335px] h-[44px] bg-[#57D061] rounded-full mt-4 cursor-pointer"
                                >
                                    {{ $urls[$course->SEOurl][1] }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <el-dialog v-if="idCurrentAnswer !== -1" :visible.sync="dialogUserResultVisible"
                   max-width="1097px"
                   width="80%"
                   height="80vh"
                   center
                   :show-close="false"
                   :close-on-click-modal="true"
                   class="dialog-user-result-lesson">
            <!-- Slot tùy chỉnh tiêu đề -->
            <template #title>
                <div class="custom-title pt-[29px] pl-[86px] pr-[23px] bg-[#F4F5FA] flex justify-between items-end rounded-[32px]">
                    <div class="text-[#07403F] font-beanbag text-[20px] ">Chi tiết bài làm</div>
                    <el-button icon="el-icon-close" circle @click="dialogUserResultVisible = false"></el-button>
                </div>
            </template>

            <div class="dialog-wrapper pl-[43px] py-[12px] pr-[38px] mx-[43px] my-[12px] custom-scrollbar bg-[#F4F5FA]">
                <div v-if="!userResult.isExerciseSpeak"
                        :style="{background: `${userResult.ratio_correct_answer >= 0.85 ? '#CEFFD8' : '#FFF1BB'}`}"
                        :class="`mb-[50px] px-[23px] py-[32px] rounded-[24px] border-[1px] border-[#e1dfdf] text-sm w-full text-[#07403F] drop-shadow-2xl`">
                    <table style="border: none">
                        <tbody style="border: none">
                        <tr style="border: none">
                            <td style="border: none" class="min-w-[140px]">
                                <h5 class="font-averta-bold  text-[16px]">Kết quả</h5>
                            </td>
                            <td style="border: none" class="text-[16px] font-averta-regular">@{{
                                (userResult.correct_answer / userResult.total_answer) >= 0.85 ? 'Đạt' :
                                'Không đạt' }}
                            </td>
                        </tr>
                        <tr style="border: none">
                            <td style="border: none" class="min-w-[150px]">
                                <h5 class="font-averta-bold text-[16px]">Điểm của bạn</h5>
                            </td>
                            <td style="border: none" class="text-[16px] font-averta-regular">
                                @{{ userResult.correct_answer }}/@{{ userResult.total_answer }} (@{{ userResult.ratio_correct_answer < 0.85 ? '<85%' : '≥85%' }})
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="border: none" class="min-w-[140px]">
                                <p class="font-averta-regular text-[14px] text-[#757575]">@{{ userResult.text_minimum_correct }}</p>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="list-result-question">
                    <div class="result-question-wrapper overscroll-auto">
                        <div v-for="(question, key) in dataLesson.component" class="result-question-item"
                             :key="key + 1">
                            <div class="result-question-wrapper p-2 mb-[36px]">
                                <div class="result-question-item-divider flex items-center">
                                    <h1 class="font-zuume-semibold text-[#57D061] font-bold mr-2">@{{ key + 1 }}
                                    </h1>
                                    <div class="flex-1 border-t border-gray-300"></div>
                                </div>
                                <div class="result-question-item-content">
                                    <template v-if="question.type === {{ \App\Http\Models\LessonToTask::MULTIPLE_CHOICE }}">
                                        <!-- Question 12312 -->
                                        <div
                                                :class="`content-question mt-6 mb-15 leading-[1.61] ${checkAnswerAudio(question) == 4 ? 'p-4 bg-white rounded-3xl shadow-lg' : ''} text-center ${ checkAnswerAudio(question) != 1 ? 'max-w-[695px]' : ''} mx-auto flex justify-content-center align-items-center font-gen-jyuu-gothic text-black MULTIPLE_CHOICE_POPUP`">
                                            <template v-if="checkComponentQuestion(question) == 1">
                                                <div class="bg-white shadow-md rounded-3xl px-[18px] py-[21px] w-full">
                                                    <!-- Icon âm thanh -->
                                                    <div class="flex">
                                                        <svg @click="toggleMp3(question.id, 'question')" class="cursor-pointer"
                                                             width="48" height="48" viewBox="0 0 48 48"
                                                             fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                    d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                                    fill="#4E87FF"/>
                                                            <path
                                                                    d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                                    fill="#4E87FF"/>
                                                            <path opacity="0.4"
                                                                  d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                                  fill="#4E87FF"/>
                                                            <path
                                                                    d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                                    fill="#4E87FF"/>
                                                        </svg>
                                                    </div>

                                                    <!-- Văn bản Furigana -->
                                                    <template v-if="checkAnswerAudio(question) == 1">
                                                        <div class="flex-1 text-center text-gray-800 mt-2 text-[20px]"
                                                             v-html="getTagImg(question)">
                                                        </div>
                                                    </template>
                                                    <template v-else>
                                                        <div class="flex-1 text-center text-gray-800 mt-2 text-[20px]"
                                                             v-html="getComponentFromQuestionStr(question)">
                                                        </div>
                                                    </template>
                                                </div>
                                            </template>
                                            <template v-else-if="checkAnswerAudio(question) == 2">
                                                <button :data-audioButton="`audioButton-${question.id}`"
                                                        :ref="`audioButton-${question.id}`"
                                                        @click="toggleMp3(question.id, 'question')"
                                                        :class="['flex items-center justify-center w-[100px] h-[100px] rounded-full bg-white text-blue-500 hover:bg-blue-100 flex-col',
                                                        isPlaying ? 'animate-blink' :
                                                        'shadow-[0_0_44px_0px] shadow-[#4E87FF80]'
                                                    ]">
                                                    <!-- Icon loa -->
                                                    <div
                                                            class="w-[50px] h-[50px] bg-blue-500 rounded-full flex items-center justify-center">
                                                        <svg width="100px" height="100px" viewBox="0 0 45 44"
                                                             fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                    d="M33.7505 30.7083C33.4505 30.7083 33.1693 30.6166 32.9068 30.4333C32.288 29.975 32.1568 29.1133 32.6255 28.5083C35.5693 24.6766 35.5693 19.3233 32.6255 15.4916C32.1568 14.8866 32.288 14.025 32.9068 13.5666C33.5255 13.1083 34.4068 13.2366 34.8755 13.8416C38.5505 18.645 38.5505 25.355 34.8755 30.1583C34.5943 30.525 34.1818 30.7083 33.7505 30.7083Z"
                                                                    fill="#4E87FF" fill-opacity="0.5"
                                                                    :class="{ 'animate-opacity': isPlaying }"/>
                                                            <path
                                                                    d="M37.1822 35.2917C36.8822 35.2917 36.6009 35.2001 36.3384 35.0167C35.7197 34.5584 35.5884 33.6967 36.0572 33.0917C41.0634 26.5651 41.0634 17.4351 36.0572 10.9084C35.5884 10.3034 35.7197 9.44172 36.3384 8.98339C36.9572 8.52505 37.8384 8.65339 38.3072 9.25839C44.0634 16.7567 44.0634 27.2434 38.3072 34.7417C38.0447 35.1084 37.6134 35.2917 37.1822 35.2917Z"
                                                                    fill="#4E87FF" fill-opacity="0.5"
                                                                    :class="{ 'animate-opacity-second': isPlaying }"/>
                                                            <path opacity="0.4"
                                                                  d="M29.5312 13.585V30.415C29.5312 33.5683 28.3688 35.9333 26.2875 37.07C25.4438 37.5283 24.5063 37.7483 23.5312 37.7483C22.0312 37.7483 20.4188 37.2533 18.7688 36.245L13.2938 32.89C12.9188 32.67 12.4875 32.5417 12.0562 32.5417H10.3125V11.4583H12.0562C12.4875 11.4583 12.9188 11.33 13.2938 11.11L18.7688 7.75499C21.5063 6.08666 24.1875 5.79333 26.2875 6.92999C28.3688 8.06666 29.5312 10.4317 29.5312 13.585Z"
                                                                  fill="#4E87FF"/>
                                                            <path
                                                                    d="M10.3125 11.4583V32.5417H9.375C4.8375 32.5417 2.34375 30.1033 2.34375 25.6667V18.3333C2.34375 13.8967 4.8375 11.4583 9.375 11.4583H10.3125Z"
                                                                    fill="#4E87FF"/>
                                                        </svg>
                                                    </div>
                                                    <!-- Thời gian -->
                                                    <span
                                                            class="text-[#4E87FF] text-sm font-averta-bold text-blue-500">@{{ formattedTime }}</span>
                                                </button>
                                            </template>
                                            <template v-else class="">
                                                <div v-html="question.value"></div>
                                            </template>

                                            {{--                            @if (false) --}}
                                            {{--                                <p class="text-lg font-semibold">このことばは　ひらがなで　どう　かきますか。</p> --}}
                                            {{--                                <p class="text-sm text-gray-500"> --}}
                                            {{--                                    1・2・3・4から　いちばん　いいものを　ひとつえらんで　ください。</p> --}}
                                            {{--                            @elseif(true) --}}
                                            {{--                                <div class="video"> --}}
                                            {{--                                    <video id="my-video" class="video-js w-[50%] h-[352px]" controls preload="auto" --}}
                                            {{--                                           poster="https://web-test.dungmori.com/cdn/lesson/default/1729757832_118153995_79687.jpeg" --}}
                                            {{--                                           data-setup="{}"> --}}
                                            {{--                                        <source --}}
                                            {{--                                                src="https://tokyo-v2.dungmori.com/720p/XHTH-2022-01-3.mp4/index.m3u8" --}}
                                            {{--                                                type="application/x-mpegURL"/> --}}
                                            {{--                                    </video> --}}
                                            {{--                                </div> --}}
                                            {{--                            @endif --}}
                                        </div>

                                        <!-- Options -->
                                        <div id="options" class="grid grid-cols-2 gap-4 mb-15 max-w-[695px] mx-auto">
                                            <template v-for="(answer, keyAnswer) in question.answers">
                                                <template v-if="checkAnswerAudio(answer) == 3 || checkAnswerAudio(answer) == 2">
                                                    <label class="block">
                                                        <input :id="answer.id" type="radio"
                                                               :data-result="answer.grade" name="answer"
                                                               @click="toggleMp3(answer.id, 'option')"
                                                               :value="answer.value" class="hidden">
                                                        <span :id="`span-option-${answer.id}`"
                                                              :data-question="question.id" :data-answer="answer.id"
                                                              :data-result="answer.grade"
                                                              :class="`text-bold span-option p-[12px] ${renderStatusOptionResult(answer)} border-[4px] rounded-full cursor-pointer flex align-items-center text-[#07403F]`">
                                                            <template>
                                                                @{{ charCode(keyAnswer) }}.
                                                            </template>
                                                            <span
                                                                    class="ml-2 text-black font-gen-jyuu-gothic w-full text-center">

                                                                <svg class="cursor-pointer" width="32" height="32"
                                                                     viewBox="0 0 48 48" fill="none"
                                                                     xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                            d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                                            fill="#4E87FF"/>
                                                                    <path
                                                                            d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                                            fill="#4E87FF"/>
                                                                    <path opacity="0.4"
                                                                          d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                                          fill="#4E87FF"/>
                                                                    <path
                                                                            d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                                            fill="#4E87FF"/>
                                                                </svg>
                                                            </span>
                                                        </span>
                                                    </label>
                                                </template> <!-- Đáp án là audio -->
                                                <template v-else>
                                                    <label class="block">
                                                        <input :id="answer.id" type="radio"
                                                               :data-result="answer.grade" name="answer"
                                                               :value="answer.value" class="hidden">
                                                        <span :id="`span-option-${answer.id}`"
                                                              :data-question="question.id" :data-answer="answer.id"
                                                              :data-result="answer.grade"
                                                              :class="`span-option-default max-h-[161px] span-option p-4 ${renderStatusOptionResult(answer)} border-[4px] rounded-${checkAnswerAudio(answer) === 4 ? '2xl' : 'full'} cursor-pointer flex items-center text-[#07403F]`">
                                                            <span class="text-bold text-[16px] leading-[1.61]">
                                                                @{{ keyAnswer + 1 }}.
                                                            </span>
                                                            <span class="ml-2 text-black font-averta-semibold font-normal text-[16px] leading-[1.61]"
                                                                  v-html="answer.value">
                                                            </span>
                                                        </span>
                                                    </label>
                                                </template>
                                            </template>
                                        </div>
                                    </template>
                                    <template v-else-if="question.type === {{ \App\Http\Models\LessonToTask::TYPE_FILL_IN_THE_BLANK }}">
{{--                                        @{{ question }}--}}
                                        <!-- Question box -->
                                        <div
                                                class="grid align-items-center w-full max-w-[80%] min-h-[300px] bg-white rounded-3xl shadow-lg p-6 text-center mx-auto FILL_IN_THE_BLANK_POPUP">
                                            <!-- Icon âm thanh -->
                                            <div v-if="question.value.audio !== null && question.value.audio !== '' && question.value.audio !== undefined" class="top-[18px] left-[21px] justify-self-start row-start-1 self-start">
                                                    <div class="flex">
                                                        <svg @click="toggleMp3(question.id, 'question')"
                                                             class="cursor-pointer" width="48" height="48"
                                                             viewBox="0 0 48 48" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                    d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                                    fill="#4E87FF"
                                                                    :class="{ 'animate-opacity': isPlaying === 'question' && playingAnswerId === question.id }"
                                                            />
                                                            <path
                                                                    d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                                    fill="#4E87FF"
                                                                    :class="{ 'animate-opacity-second': isPlaying === 'question' && playingAnswerId === question.id }"
                                                            />
                                                            <path opacity="0.4"
                                                                  d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                                  fill="#4E87FF"/>
                                                            <path
                                                                    d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                                    fill="#4E87FF"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                            <!-- End Icon âm thanh -->
                                            <div v-if="(question.value.img === null || question.value.img === '') && question.value.text_vi !== undefined && question.value.text_vi !== '' && question.value.text_vi !== null" >
                                                <div class="justify-self-start font-gen-jyuu-gothic text-[16px] text-[#1E1E1E] w-full">
                                                    <div>@{{ question.value.text_vi }}</div>
                                                </div>
                                                <el-divider></el-divider>
                                            </div>

                                            <div v-if="question.value.img !== null && question.value.img !== '' && question.value.img !== undefined" class="justify-self-center max-h-[260px] mb-[24px]">
                                                <image-with-fallback
                                                    :src="`/cdn/lesson/default/${question.value.img}`"
                                                    :fallback-src="`/upload/lessons/${question.value.img}`"
                                                    :alt="`Image for question`"
                                                />
                                            </div>

                                            <div
                                                    class="text-2xl font-bold justify-content-center flex-wrap items-baseline inline leading-[54px]">
                                                <template v-for="(element, keyElement) in question.value['question']">
                                                    <template v-if="element.type === 'default'">
                                                        <div class="my-2 ml-[6px] missing-word-wrap text-[20px]"
                                                             v-html="element.value"></div>
                                                    </template>
                                                    <template v-else>
                                                        <span class="ml-[6px] my-2">
                                                            <!-- Input field -->
                                                            <input type="text" :value="element.user_result"
                                                                   :id="`missingWord-${keyElement}-${question.id}`"
                                                                   :data-id="question.id" :data-result="element.result"
                                                                   disabled
                                                                   ref="inputFillInBlank"
                                                                   :class="`missingWord missingWord-${question.id} outline-none min-w-[73px] rounded-[12px] ${renderStatusFillInBlankResult(element, keyElement, question.id)} text-center h-12 font-bold text-[24px]`"
                                                                   @input="checkInput">
                                                        </span>
                                                    </template>
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else-if="question.type === {{ \App\Http\Models\LessonToTask::TYPE_SENTENCE_JUMBLE }}">
                                        <div class="grid items-stretch justify-items-stretch align-items-center w-full max-w-[80%] min-h-[250px] bg-white rounded-3xl shadow-lg p-6 mx-auto mb-20 SENTENCE_JUMBLE_POPUP">
                                            <div class="content-question">
                                                {{--                                                                sắp xếp câu--}}
                                                <!-- Icon âm thanh có ảnh hoặc text-->
                                                <div
                                                        v-if="question.value.audio !== null && question.value.audio !== '' && ((question.value.title_question !== '' && question.value.title_question !== null) || (question.value.img !== null && question.value.img !== ''))"
                                                        class="top-[18px] left-[21px] justify-self-start row-start-1 self-start 123123123123123 mb-7">
                                                    <div class="flex items-center justify-center justify-items-center border-2 border-[#4E87FF] rounded-full py-1 px-2"
                                                         :class="[(playingAnswerId === question.id && isPlaying === 'question') ? 'animate-blink-small' : 'shadow-[0_0_15px_4px] shadow-[#4E87FF80]']"
                                                    >
                                                        <svg
                                                                @click="toggleMp3(question.id, 'question')"
                                                                class="cursor-pointer"
                                                                width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <g clip-path="url(#clip0_4414_5169)">
                                                                <rect width="34" height="34" rx="17" fill="white"/>
                                                                <path d="M25.5004 23.7292C25.2737 23.7292 25.0612 23.6584 24.8629 23.5167C24.3954 23.1626 24.2962 22.4967 24.6504 22.0292C26.8746 19.0684 26.8746 14.9317 24.6504 11.9709C24.2962 11.5034 24.3954 10.8376 24.8629 10.4834C25.3304 10.1292 25.9962 10.2284 26.3504 10.6959C29.1271 14.4076 29.1271 19.5926 26.3504 23.3042C26.1379 23.5876 25.8262 23.7292 25.5004 23.7292Z"
                                                                      fill="#4E87FF"
                                                                      :class="{ 'animate-opacity': isPlaying === 'question' && playingAnswerId === question.id }"
                                                                />
                                                                <path d="M28.0922 27.2715C27.8655 27.2715 27.653 27.2007 27.4547 27.059C26.9872 26.7049 26.888 26.039 27.2422 25.5715C31.0247 20.5282 31.0247 13.4732 27.2422 8.42988C26.888 7.96238 26.9872 7.29655 27.4547 6.94238C27.9222 6.58821 28.588 6.68738 28.9422 7.15488C33.2914 12.949 33.2914 21.0524 28.9422 26.8465C28.7439 27.1299 28.418 27.2715 28.0922 27.2715Z"
                                                                      fill="#4E87FF"
                                                                      :class="{ 'animate-opacity-second': isPlaying === 'question' && playingAnswerId === question.id }"
                                                                />
                                                                <path opacity="0.4" d="M22.3118 10.4969V23.5019C22.3118 25.9386 21.4335 27.7661 19.861 28.6444C19.2235 28.9986 18.5152 29.1686 17.7785 29.1686C16.6452 29.1686 15.4268 28.7861 14.1802 28.0069L10.0435 25.4144C9.76018 25.2444 9.43435 25.1453 9.10852 25.1453H7.79102V8.85361H9.10852C9.43435 8.85361 9.76018 8.75445 10.0435 8.58445L14.1802 5.99195C16.2485 4.70278 18.2743 4.47611 19.861 5.35445C21.4335 6.23278 22.3118 8.06028 22.3118 10.4969Z"
                                                                      fill="#4E87FF"/>
                                                                <path d="M7.79232 8.85449V25.1462H7.08398C3.65565 25.1462 1.77148 23.262 1.77148 19.8337V14.167C1.77148 10.7387 3.65565 8.85449 7.08398 8.85449H7.79232Z" fill="#4E87FF"/>
                                                            </g>
                                                            <defs>
                                                                <clipPath id="clip0_4414_5169">
                                                                    <rect width="34" height="34" rx="17" fill="white"/>
                                                                </clipPath>
                                                            </defs>
                                                        </svg>
                                                        <div class="ml-2 text-[#4E87FF] text-xs font-averta-regular text-blue-500">
                                                            @{{ audioTimes[question.id]?.formattedTime || '00:00' }} / @{{ audioTimes[question.id]?.duration || '00:00' }}
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- chỉ có Icon âm thanh-->
                                                <div
                                                        v-if="question.value.audio !== null && question.value.audio !== '' && ((question.value.title_question === '' || question.value.title_question === null) && (question.value.img === null || question.value.img === ''))"
                                                        class="items-center flex my-10 justify-center">
                                                    <button :data-audioButton="`audioButton-${question.id}`"
                                                            ref="`audioButton-${question.id}`"
                                                            @click="toggleMp3(question.id, 'question')"
                                                            :class="['flex items-center justify-center w-[100px] h-[100px] rounded-full bg-white text-blue-500 hover:bg-blue-100 flex-col', (playingAnswerId === question.id && isPlaying) ? 'animate-blink' : 'shadow-[0_0_15px_4px] shadow-[#4E87FF80]']">
                                                        <!-- Icon loa -->
                                                        <div
                                                                class="w-[50px] h-[50px] bg-blue-500 rounded-full flex items-center justify-center">
                                                            <svg width="100px" height="100px" viewBox="0 0 45 44"
                                                                 fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                        d="M33.7505 30.7083C33.4505 30.7083 33.1693 30.6166 32.9068 30.4333C32.288 29.975 32.1568 29.1133 32.6255 28.5083C35.5693 24.6766 35.5693 19.3233 32.6255 15.4916C32.1568 14.8866 32.288 14.025 32.9068 13.5666C33.5255 13.1083 34.4068 13.2366 34.8755 13.8416C38.5505 18.645 38.5505 25.355 34.8755 30.1583C34.5943 30.525 34.1818 30.7083 33.7505 30.7083Z"
                                                                        fill="#4E87FF" fill-opacity="0.5"
                                                                        :class="{ 'animate-opacity': isPlaying && playingAnswerId === question.id }" />
                                                                <path
                                                                        d="M37.1822 35.2917C36.8822 35.2917 36.6009 35.2001 36.3384 35.0167C35.7197 34.5584 35.5884 33.6967 36.0572 33.0917C41.0634 26.5651 41.0634 17.4351 36.0572 10.9084C35.5884 10.3034 35.7197 9.44172 36.3384 8.98339C36.9572 8.52505 37.8384 8.65339 38.3072 9.25839C44.0634 16.7567 44.0634 27.2434 38.3072 34.7417C38.0447 35.1084 37.6134 35.2917 37.1822 35.2917Z"
                                                                        fill="#4E87FF" fill-opacity="0.5"
                                                                        :class="{ 'animate-opacity-second': isPlaying && playingAnswerId === question.id }" />
                                                                <path opacity="0.4"
                                                                      d="M29.5312 13.585V30.415C29.5312 33.5683 28.3688 35.9333 26.2875 37.07C25.4438 37.5283 24.5063 37.7483 23.5312 37.7483C22.0312 37.7483 20.4188 37.2533 18.7688 36.245L13.2938 32.89C12.9188 32.67 12.4875 32.5417 12.0562 32.5417H10.3125V11.4583H12.0562C12.4875 11.4583 12.9188 11.33 13.2938 11.11L18.7688 7.75499C21.5063 6.08666 24.1875 5.79333 26.2875 6.92999C28.3688 8.06666 29.5312 10.4317 29.5312 13.585Z"
                                                                      fill="#4E87FF"/>
                                                                <path
                                                                        d="M10.3125 11.4583V32.5417H9.375C4.8375 32.5417 2.34375 30.1033 2.34375 25.6667V18.3333C2.34375 13.8967 4.8375 11.4583 9.375 11.4583H10.3125Z"
                                                                        fill="#4E87FF"/>
                                                            </svg>
                                                        </div>
                                                        <!-- Thời gian -->
                                                        <span
                                                                class="text-[#4E87FF] text-xs font-averta-regular text-blue-500">
                                                            @{{ audioTimes[question.id]?.formattedTime || '00:00' }} / @{{ audioTimes[question.id]?.duration || '00:00' }}
                                                        </span>
                                                    </button>
                                            </div>
                                                <!-- End Icon âm thanh -->

                                            <div
                                                    v-if="question.value.img !== null && question.value.img !== ''"
                                                    class="justify-self-center max-h-[260px] mb-[24px]">
                                                <image-with-fallback
                                                        :src="`/cdn/lesson/default/${question.value.img}`"
                                                        :fallback-src="`/upload/lessons/${question.value.img}`"
                                                        :alt="`Image for question`"
                                                />
                                            </div>
                                            <div
                                                    v-if="question.value.title_question !== undefined && question.value.title_question !== '' && question.value.title_question !== null"
                                                    class="justify-self-start font-gen-jyuu-gothic text-[16px] text-[#1E1E1E] w-full text-center mt-5"
                                                v-html="question.value.title_question"
                                            >
                                            </div>
                                            </div>
                                            <div class="divider div-transparent div-arrow-down my-5"></div>

                                            <div v-if="userResult.data[question.id]" class="wrapper-user-result flex justify-center flex-wrap" :data-id="question.id">
                                                <template v-if="Object.keys(userResult.data[question.id]).length > Object.keys(question.value.question).length">
                                                    <div
                                                            v-for="(item, key) in userResult.data[question.id]"
                                                            :data-uuid="item"
                                                            class="sentence-jumble-item-result border-[3px] rounded-[12px] m-2 p-3 font-gen-jyuu-gothic font-medium text-2xl cursor-pointer select-none"
                                                            :class="question.value.question[key] != undefined && question.value.question[key].uuid == item ? 'text-black bg-[#95FF99] border-[#57D061]' : 'text-black bg-[#FDD3D0] border-[#FF7C79]'"
                                                            v-html="renderSelectWordSentenceJumble(item, question.id)"
                                                    >
                                                    </div>
                                                </template>
                                                <template v-else>
                                                    <template v-for="(item, key) in question.value.question">
                                                        <div :data-uuid="item.uuid"
                                                             class="sentence-jumble-item-result border-[3px] rounded-[12px] m-2 p-3 font-gen-jyuu-gothic font-medium text-2xl cursor-pointer select-none"
                                                             :class="key >= Object.keys(userResult.data[question.id]).length ? 'sentence-jumble-item-result-hide bg-[#FDD3D0] border-[#FF7C79] text-[#FDD3D0]' : (userResult.data[question.id][key] != item.uuid ? 'text-black bg-[#FDD3D0] border-[#FF7C79]' : 'text-black bg-[#95FF99] border-[#57D061]')"
                                                             v-html="key < Object.keys(userResult.data[question.id]).length ? renderSelectWordSentenceJumble(userResult.data[question.id][key], question.id) : item.value"></div>
                                                    </template>
                                                </template>
                                            </div>
                                        </div>
                                        <div class="flex justify-center flex-wrap">
                                            <div v-for="item in question.value.items"
                                                    :data-uuid="item.uuid"
                                                    class="sentence-jumble-item border-[3px] rounded-[12px] m-2 p-3 font-gen-jyuu-gothic font-medium text-2xl cursor-pointer select-none"
                                                     :class="userResult.data[question.id] && userResult.data[question.id].includes(item.uuid) ? 'bg-[#D9D9D9] border-[#D9D9D9] text-[#D9D9D9]' : 'bg-[#F5F5F5] text-black'"
                                                     v-html="item.value"
                                                >
                                                </div>
                                        </div>
                                    </template>
                                    <template v-else-if="question.type === 15">
                                        <div class="w-full justify-center flex gap-[25px]">
                                            <div
                                                class="hidden
                                            border-[#CFF7FF] border-[#FFF8C7] border-[#E2E3FC] border-[#FFDFC9] border-[#FCDAFF] border-transparent
                                            hover:border-[#CFF7FF] hover:border-[#FFF8C7] hover:border-[#E2E3FC] hover:border-[#FFDFC9] hover:border-[#FCDAFF] hover:border-transparent
                                            bg-[#CFF7FF] bg-[#FFF8C7] bg-[#E2E3FC] bg-[#FFDFC9] bg-[#FCDAFF]
                                            "></div>

                                            <div v-if="getResultPairQuestion(question.id) && getResultPairQuestion(question.id).left && getResultPairQuestion(question.id).right" class="flex flex-col gap-[20px]">
                                                <div v-for="(block, idx) in getResultPairQuestion(question.id).left" :key="`pair-block-${block.uuid}`" class="flex items-stretch justify-between gap-[20px]">
                                                    <div
                                                        class="w-[300px] rounded-[20px] bg-white min-h-[100px] flex items-center justify-center cursor-pointer transition border-[6px] p-4"
                                                        :class="[
                                                        getLeftPairIndex(block.uuid, pairCounter, question.id) !== -1 ? `bg-[${getLeftPairColor(block.uuid, pairCounter, question.id)}]` : '',
                                                        checkPairAnswer(block.uuid, 'left', question.id) ? 'border-[#57D061] hover:border-[#57D061]' : 'border-[#FF7C79] hover:border-[#FF7C79]'
                                                    ]"
                                                    >
                                                        <span v-html="block.value" class="font-beanbag-regular text-[16px] text-[#1E1E1E] break-words" style="word-break: break-word"></span>
                                                    </div>
                                                    <div
                                                        class="w-[300px] rounded-[20px] bg-white min-h-[100px] flex items-center justify-center cursor-pointer transition border-[6px] p-4"
                                                        :class="[
                                                        getRightPairIndex(getResultPairQuestion(question.id).right[idx].uuid, pairCounter, question.id) !== -1 ? `bg-[${getRightPairColor(getResultPairQuestion(question.id).right[idx].uuid, pairCounter, question.id)}]` : '',
                                                        checkPairAnswer(getResultPairQuestion(question.id).right[idx].uuid, 'right', question.id) ? 'border-[#57D061] hover:border-[#57D061]' : 'border-[#FF7C79] hover:border-[#FF7C79]'
                                                    ]"
                                                    >
                                                        <span v-html="getResultPairQuestion(question.id).right[idx].value" class="font-beanbag-regular text-[16px] text-[#1E1E1E] break-words" style="word-break: break-word"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else-if="question.type === {{ \App\Http\Models\LessonToTask::TYPE_SPEAKING }}">
                                        <div class="grid items-stretch justify-items-stretch align-items-center w-full max-w-[80%] min-h-[250px] bg-white rounded-3xl shadow-lg p-6 mx-auto mb-20 SPEAKING_POPUP">
                                            <div class="content-question">
                                                <div v-if="question['value']['img'] !== null && question['value']['img'] !== ''" class="justify-self-center max-h-[260px] mb-[24px]">
                                                    <image-with-fallback
                                                            :src="`/cdn/lesson/default/${question.value.img}`"
                                                            :fallback-src="`/upload/lessons/${question.value.img}`"
                                                            :alt="`Image for question`"
                                                    />
{{--                                                    <img--}}
{{--                                                            class="h-[90%] w-auto object-cover"--}}
{{--                                                            src="{{ file_exists(public_path('upload/lessons/' . $component->value['img'])) ? '/upload/lessons/' . $component->value['img'] : '/cdn/lesson/default/' . $component->value['img'] }}">--}}
                                                </div>
                                                <div class="flex justify-center rounded-2xl border-[#E1EBFF] border-2">
                                                    <div class="w-1/2">
                                                        <div
                                                            :class="[
                                                                !(currentPlaying && currentPlaying.id === question.id && currentPlaying.type === 'questionResult' && currentPlaying.componentData === 'slowWave') ? 'font-averta-bold text-[#4E87FF]' : 'font-averta-regular text-[#B3B3B3]'
                                                            ]"
                                                            class="text-base text-center  py-3"
                                                        >
                                                            <button
                                                                    @click="toggleMp3(question.id, 'questionResult', 'defaultWave')"
                                                            >
                                                                Tiêu chuẩn 1x
                                                            </button>
                                                        </div>
                                                        <div class="bg-[#E1EBFF] rounded-tl-2xl p-3 flex items-center gap-2 w-full">
                                                            <!-- Nút Play/Pause -->
                                                            <div style="width: 20px">
                                                                <button @click="toggleMp3(question.id, 'questionResult', 'defaultWave')" class="text-blue-600">
                                                                    <span v-if="currentPlaying && currentPlaying.id === question.id && currentPlaying.type === 'questionResult'">
                                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                            <path d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                                                                                  fill="#4E87FF"/>
                                                                            <path d="M20.9996 19.11V4.89C20.9996 3.54 20.4296 3 18.9896 3H15.3596C13.9296 3 13.3496 3.54 13.3496 4.89V19.11C13.3496 20.46 13.9196 21 15.3596 21H18.9896C20.4296 21 20.9996 20.46 20.9996 19.11Z"
                                                                                  fill="#4E87FF"/>
                                                                        </svg>
                                                                    </span>
                                                                    <span v-else>
                                                                        <svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                            <path d="M15 7.26795C16.3333 8.03775 16.3333 9.96225 15 10.7321L3 17.6603C1.66667 18.4301 1.01267e-06 17.4678 1.07997e-06 15.9282L1.68565e-06 2.0718C1.75295e-06 0.532196 1.66667 -0.430054 3 0.339746L15 7.26795Z"
                                                                                  fill="#4E87FF"/>
                                                                        </svg>
                                                                    </span>
                                                                </button>
                                                            </div>

                                                            <!-- Waveform -->
                                                            <div :ref="`${question.id}-default-waveform-result`" :class="`${question.id}-default-waveform-result`" class="default-waveform-result flex-1 h-10"></div>
                                                        </div>
                                                    </div>
                                                    <div class="w-1/2">
                                                        <div
                                                                :class="[
                                                                    (currentPlaying && currentPlaying.id === question.id && currentPlaying.type === 'questionResult' && currentPlaying.componentData === 'slowWave') ? 'font-averta-bold text-[#4E87FF]' : 'font-averta-regular text-[#B3B3B3]'
                                                                ]"
                                                                class=" text-base text-center py-3">
                                                            <button
                                                                    @click="toggleMp3(question.id, 'questionResult', 'slowWave')"
                                                            >
                                                                Chậm 0.75x
                                                            </button>
                                                        </div>
                                                        <div class="bg-[#E1EBFF] rounded-tr-2xl p-3 flex items-center gap-2 w-full">
                                                            <!-- Waveform -->
                                                            <div :ref="`${question.id}-slow-waveform-result`" class="flex-1 h-10"></div>

                                                            <!-- Thời gian -->
                                                            <div class="text-base font-averta-regular text-[#4E87FF]">
                                                                @{{ audioTimes[question.id]?.formattedTime || '00:00' }} / @{{ audioTimes[question.id]?.duration || '00:00' }}
                                                            </div>

                                                            <!-- Nút âm lượng -->
                                                            <button class="text-blue-600">
                                                                <img
                                                                    v-if="statusSpeak.recordDefaultStatus == 'mic' || statusSpeak.recordSlowStatus == 'mic'"
                                                                    src="{{ asset("/images/lessons/svg/icon-audio-small-disable.svg") }}">
                                                                <img v-else src="{{ asset("/images/lessons/svg/icon-audio-small.svg") }}">
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="flex mb-5">
                                            <div class="speak-slow w-1/2 mr-2">
                                                <div class="flex items-center">
                                                    <div class="rounded-[50%] h-[40px] w-[40px] justify-center justify-items-center flex items-center mr-1 bg-[#CCF8D1] text-2xl">1</div>
                                                    <div class="text-xl text-[#07403F] font-averta-regular">Luyện nói tốc độ chậm</div>
                                                    <div class="ml-2">
                                                        <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M18 2L7 13L2 8" stroke="#07403F" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div v-html="question.value.title_question" id="wrap" class="run-text text-[#757575] font-gen-jyuu-gothic text-xl mt-6">
                                                </div>
                                                <div class="record-wrap flex mt-3">
                                                    <div class="record-content w-full rounded-full flex items-center p-2 bg-[#ccf8d1]">
                                                        <div class="w-[20px] btn-toggle-play">
                                                            <button class="cursor-pointer" @click="toggleMp3(question.id, 'recordResult', 'recordSlow')">
                                                                <img v-if="currentPlaying && currentPlaying.componentData === 'recordSlow' && currentPlaying.type === 'recordResult' && currentPlaying.id === question.id" src="{{ asset('images/lessons/svg/pause-record.svg') }}">
                                                                <img v-else src="{{ asset('images/lessons/svg/play-record.svg') }}">
                                                            </button>
                                                        </div>
                                                        <div class=" w-[77%] px-2 wave-surfer-record">
                                                            <div :ref="`${question.id}-recordSlow-result`" :class="`${question.id}-recordSlow-result`" class="record-audio-wrap-result slow-record-wrap"></div>
                                                        </div>
                                                        <div
                                                                :class="`recordSlowTime-${question.id}`"
                                                                class="self-center recordSlowTime rounded-full font-averta-regular text-xs ml-2 px-2 bg-[#F0FFF1] text-[#07403F]">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="speak-default w-1/2 ml-2">
                                                <div class="flex items-center">
                                                    <div class="rounded-[50%] h-[40px] w-[40px] justify-center justify-items-center flex items-center mr-1 bg-[#CCF8D1] text-2xl">2</div>
                                                    <div class="text-xl text-[#07403F] font-averta-regular">Luyện nói tốc độ tiêu chuẩn</div>
                                                    <div class="ml-2">
                                                        <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M18 2L7 13L2 8" stroke="#07403F" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div v-html="question.value.title_question" id="wrap" class="run-text text-[#757575] font-gen-jyuu-gothic text-xl mt-6">
                                                </div>
                                                <div class="record-wrap flex mt-3">
                                                    <div class="record-content w-full rounded-full flex items-center p-2 bg-[#ccf8d1]">
                                                        <div class="w-[20px] btn-toggle-play">
                                                            <button class="cursor-pointer" @click="toggleMp3(question.id, 'recordResult', 'recordDefault')">
                                                                <img v-if="currentPlaying && currentPlaying.componentData === 'recordDefault' && currentPlaying.type === 'recordResult' && currentPlaying.id === question.id" src="{{ asset('images/lessons/svg/pause-record.svg') }}">
                                                                <img v-else src="{{ asset('images/lessons/svg/play-record.svg') }}">
                                                            </button>
                                                        </div>
                                                        <div class=" w-[77%] px-2 wave-surfer-record">
                                                            <div :ref="`${question.id}-recordDefault-result`" :class="`${question.id}-recordDefault-result`" class="record-audio-wrap-result slow-record-wrap"></div>
                                                        </div>
                                                        <div
                                                                :class="`recordDefaultTime-${question.id}`"
                                                                class="self-center recordDefaultTime rounded-full font-averta-regular text-xs ml-2 px-2 bg-[#F0FFF1] text-[#07403F]">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else></template>
                                    <div class="max-w-[695px] mx-auto result-question-item-explain mt-[28px]"
                                         v-if="
                                         (question.type === 3 && question.explain !== null && question.explain !== '')
                                         || ([13, 14, 15].includes(question.type) && question.value['explain'] !== null && question.value['explain'] !== '') || (question.explain_mp3 !== null && question.explain_mp3 !== '')
                                         ">
                                        <div class="px-[12px] py-[9px] rounded-[16px] text-sm w-full text-[#1E1E1E] bg-[#F0FFF1]">
                                            <div class="uppercase font-beanbag flex justify-between items-center">
                                                <span>*Giải thích</span>
                                                <div>
                                                    <button @click="toggleMp3(question.id, 'explain_mp3')" v-if="question.explain_mp3" class="text-blue-600 focus:outline-none" :data-id="`audio-${question.id}`"
                                                            :id="`explainButtonAudio${question.id}`">
                                                        <svg width="40" height="40" viewBox="0 0 40 40" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <rect x="1" y="1" width="38" height="38" rx="19"
                                                                  stroke="#4E87FF" stroke-width="2"/>
                                                            <path
                                                                d="M26.0003 24.75C25.8403 24.75 25.6903 24.7 25.5503 24.6C25.2203 24.35 25.1503 23.88 25.4003 23.55C26.9703 21.46 26.9703 18.54 25.4003 16.45C25.1503 16.12 25.2203 15.65 25.5503 15.4C25.8803 15.15 26.3503 15.22 26.6003 15.55C28.5603 18.17 28.5603 21.83 26.6003 24.45C26.4503 24.65 26.2303 24.75 26.0003 24.75Z"
                                                                fill="#4E87FF"/>
                                                            <path
                                                                d="M27.8304 27.25C27.6704 27.25 27.5204 27.2 27.3804 27.1C27.0504 26.85 26.9804 26.38 27.2304 26.05C29.9004 22.49 29.9004 17.51 27.2304 13.95C26.9804 13.62 27.0504 13.15 27.3804 12.9C27.7104 12.65 28.1804 12.72 28.4304 13.05C31.5004 17.14 31.5004 22.86 28.4304 26.95C28.2904 27.15 28.0604 27.25 27.8304 27.25Z"
                                                                fill="#4E87FF"/>
                                                            <path opacity="0.4"
                                                                  d="M23.75 15.41V24.59C23.75 26.31 23.13 27.6 22.02 28.22C21.57 28.47 21.07 28.59 20.55 28.59C19.75 28.59 18.89 28.32 18.01 27.77L15.09 25.94C14.89 25.82 14.66 25.75 14.43 25.75H13.5V14.25H14.43C14.66 14.25 14.89 14.18 15.09 14.06L18.01 12.23C19.47 11.32 20.9 11.16 22.02 11.78C23.13 12.4 23.75 13.69 23.75 15.41Z"
                                                                  fill="#4E87FF"/>
                                                            <path
                                                                d="M13.5 14.25V25.75H13C10.58 25.75 9.25 24.42 9.25 22V18C9.25 15.58 10.58 14.25 13 14.25H13.5Z"
                                                                fill="#4E87FF"/>
                                                        </svg>
                                                    </button>
{{--                                                    <audio  v-if="question.explain_mp3" :id="`audio-${question.id}`" :src="`/cdn/audio/${question.explain_mp3}`"></audio>--}}
                                                </div>
                                            </div>
                                            <div
                                                v-html="question.type === 3 ? question.explain : question.value['explain']"
                                                style="word-break: break-word; line-height: 2">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>

        <el-dialog v-if="idCurrentAnswer !== -1" :visible.sync="dialogInstruction"
                   max-width="1097px"
                   width="80%"
                   height="80vh"
                   center
                   :show-close="false"
                   :close-on-click-modal="true"
                   class="dialog-user-instruction">
            <!-- Slot tùy chỉnh tiêu đề -->
            <template #title>
                <div class="custom-title pt-[21px] pl-[86px] pr-[23px] flex justify-end items-end rounded-[32px]">
                    <button @click="dialogInstruction = false" >Bỏ qua >></button>
                </div>
            </template>
            <div class="dialog-wrapper text-[#07403F] text-center">
                <div class="font-beanbag text-bold text-2xl mb-[20px]">
                    Bài tập <span class="text-white py-[2px] px-[8px] bg-[#EF6D13] rounded-xl">mới</span>
                </div>
                <div class="font-zuume-semibold text-6xl text-[#07403F] uppercase mb-[20px]">
                    Luyện phát âm
                </div>
                <div class="font-beanbag-regular text-lg text-[#07403F] mb-[20px] max-w-2xl mx-auto">
                    Dạng bài giúp bạn luyện <span class="text-bold font-beanbag inline-block">phát âm chuẩn</span>, điều chỉnh âm điệu, tăng độ <span class="text-bold font-beanbag inline-block">lưu loát</span> khi nói và <span class="text-bold font-beanbag inline-block">rèn khả năng nghe</span>!
                </div>

                <div class="slide-dialog-instruction">
                    <div>
                        <img src="{{ asset("images/lessons/svg/speak-step-1.svg") }}">
                    </div>
                    <div>
                        <img src="{{ asset("images/lessons/svg/speak-step-2.svg") }}">
                    </div>
                    <div>
                        <img src="{{ asset("images/lessons/svg/speak-step-3.svg") }}">
                    </div>
                </div>

                <div class="mt-8">
                    <button
                            id="btn-next-step-dialog-instruction"
                            @click="nextStepSlidePopupInstruction()"
                            class="rounded-full text-[#07403F] text-center font-averta-bold bg-[#CCF8D1] shadow-lg p-4 uppercase text-xs"
                    >Mình đã hiểu, tiếp đi></button>
                </div>
            </div>
        </el-dialog>
        @include('frontend.course.components.modal-confirm-leave')
        @include('frontend.course.components.modal-confirm-leave-exam')
    </div>
@stop

<style>
    .sentence-jumble-item-result p:has(ruby), .sentence-jumble-item p:has(ruby) {
        margin-top: -1px;
        margin-bottom: -17px;
    }
    .arrow_box {
        position: relative;
        background: #D9D9D9;
    }
    .arrow_box:after {
        top: 100%;
        left: 50%;
        border: solid transparent;
        content: "";
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
        border-color: rgba(136, 183, 213, 0);
        border-top-color: #D9D9D9;
        border-width: 7px;
        margin-left: -7px;
    }
    .divider
    {
        position: relative;
        margin-top: 90px;
        height: 1px;
    }

    .div-transparent:before
    {
        content: "";
        position: absolute;
        top: 0;
        left: 5%;
        right: 5%;
        width: 90%;
        height: 1px;
        background-image: linear-gradient(to right, rgba(208, 211, 218, 1), rgba(208, 211, 218, 1));
    }

    .div-arrow-down:after
    {
        content: "";
        position: absolute;
        z-index: 1;
        top: -6px;
        left: calc(22% - 7px);
        width: 14px;
        height: 14px;
        transform: rotate(45deg);
        background-color: white;
        border-bottom: 1px solid rgba(208, 211, 218, 1);
        border-right: 1px solid rgba(208, 211, 218, 1);
    }

    .content-question img {
        max-width: 100%; /* Giới hạn ảnh không vượt quá kích thước của div */
        height: auto; /* Tự động điều chỉnh chiều cao */
        max-height: 286px;
        display: block; /* Đảm bảo không có khoảng trắng dư */
        margin: 0 auto; /* Căn giữa ảnh trong div */
        object-fit: contain; /* Thu nhỏ ảnh vừa với khung */
    }

    /* Định nghĩa animation nhấp nháy */
    @keyframes blink {
        0%,
        100% {
            box-shadow: 0 0 44px 25px rgba(78, 135, 255, 0.5);
            /* shadow-[0_0_44px_4px] */
            -webkit-box-shadow: 0 0 44px 25px rgba(78, 135, 255, 0.51);
            -moz-box-shadow: 0 0 44px 25px rgba(78, 135, 255, 0.51);
        }

        50% {
            box-shadow: 0 0 44px 44px rgba(78, 135, 255, 0.5);
            /* shadow-[0_0_44px_25px] */
            -webkit-box-shadow: 0 0 44px 44px rgba(78, 135, 255, 0.51);
            -moz-box-shadow: 0 0 44px 44px rgba(78, 135, 255, 0.51);
        }
    }

    @keyframes blink-mini {
        0%,
        100% {
            box-shadow: 0 0 15px 7px rgba(78, 135, 255, 0.5);
            /* shadow-[0_0_44px_4px] */
            -webkit-box-shadow: 0 0 15px 7px rgba(78, 135, 255, 0.51);
            -moz-box-shadow: 0 0 15px 7px rgba(78, 135, 255, 0.51);
        }

        50% {
            box-shadow: 0 0 15px 15px rgba(78, 135, 255, 0.5);
            /* shadow-[0_0_44px_25px] */
            -webkit-box-shadow: 0 0 15px 15px rgba(78, 135, 255, 0.51);
            -moz-box-shadow: 0 0 15px 15px rgba(78, 135, 255, 0.51);
        }
    }

    @keyframes record-blur {
        0%,
        100% {
            box-shadow: 0 0 15px 7px rgba(255, 185, 143, 1);
            /* shadow-[0_0_44px_4px] */
            -webkit-box-shadow: 0 0 15px 7px rgba(255, 185, 143, 1);
            -moz-box-shadow: 0 0 15px 7px rgba(255, 185, 143, 1);
        }

        50% {
            box-shadow: 0 0 15px 15px rgba(255, 185, 143, 1);
            /* shadow-[0_0_44px_25px] */
            -webkit-box-shadow: 0 0 15px 15px rgba(255, 185, 143, 1);
            -moz-box-shadow: 0 0 15px 15px rgba(255, 185, 143, 1);
        }
    }

    .animate-blink {
        animation: blink 1s infinite;
        /* Thời gian 1s và lặp vô hạn */
    }

    .animate-blink-small {
        animation: blink-mini 1s infinite;
    }

    .animate-record-blur {
        animation: record-blur 1s infinite;
    }

    @keyframes blinkOpacity {

        0%,
        100% {
            fill-opacity: 0.5;
        }

        50% {
            fill-opacity: 1;
        }
    }

    @keyframes blinkOpacityFirst {
        0% {
            fill-opacity: 1;
        }

        /* Hiện lên tại giây đầu tiên */
        25% {
            fill-opacity: 0.5;
        }

        /* Tắt đi tại giây thứ hai */
        50% {
            fill-opacity: 0.5;
        }

        /* Giữ nguyên tắt cho đến giữa chu kỳ */
        100% {
            fill-opacity: 1;
        }

        /* Hiện lên lại ở giây thứ tư */
    }

    .animate-opacity {
        animation: blinkOpacityFirst 2s infinite;
        /* Chu kỳ 4s lặp lại vô hạn */
        /*animation-delay: 1s; !* Chờ 1s trước khi bắt đầu *!*/
    }

    .animate-opacity-second {
        animation: blinkOpacity 1s infinite;
        /* Thời gian 1s và lặp vô hạn */
    }

    .span-option img {
        max-height: 100px;
        object-fit: contain;
        min-width: 250px;
        margin: 0 auto;
    }

    .missing-word-wrap {
        display: inline;
    }

    .missing-word-wrap p {
        font-size: 24px;
        display: inline;
    }

    .missingWord:focus {
        border: 3px solid #4E87FF;
    }

    .missingWord {
        width: 73px;
        /*-webkit-transition: 0.5s;*/
    }

    /*
  Enter and leave animations can use different
  durations and timing functions.
*/
    .slide-fade-enter-active {
        transition: all 2s ease-out;
    }

    .slide-fade-leave-active {
        transition: all 2s cubic-bezier(1, 0.5, 0.8, 1);
    }

    .slide-fade-enter-from,
    .slide-fade-leave-to {
        transform: translateX(-20px);
        opacity: 0;
    }

    /* tooltip body */
    .el-tooltip__popper.is-customized {
        background: #FFBE97B8;
        color: #07403F;
        font-size: 14px;
        border-radius: 15px;
        font-family: "Averta-Semibold", arial;
        font-weight: 400;
    }

    /* tooltip arrow body */
    .el-tooltip__popper.is-customized .popper__arrow {
        border-right-color: #FFBE97B8;
    }

    /* tooltip arrow border */
    .el-tooltip__popper.is-customized .popper__arrow::after {
        border-right-color: #FFBE97B8;
    }

    .link-review-result-lesson:hover {
        color: #57D061 !important;
        text-decoration: underline !important;
    }

    .dialog-user-result-lesson .el-dialog__body .custom-scrollbar {
        max-height: 65%;
        overflow-y: auto;
    }

    /* Style thanh cuộn */
    .dialog-user-result-lesson .custom-scrollbar {
        /*scrollbar-width: thin;*/
        /*scrollbar-color: #57D061 #F4F5FA;*/
    }

    .dialog-user-result-lesson .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
        background-color: #D9D9D9;
        border-radius: 10px;
    }

    .dialog-user-result-lesson .custom-scrollbar::-webkit-scrollbar-track {
        border-radius: 6px;
        background-color: #D9D9D9;
    }

    .dialog-user-result-lesson .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #57D061;
        border-radius: 10px;
    }

    .dialog-user-result-lesson .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #45B052;
    }

    .dialog-user-result-lesson .el-dialog__header,
    .dialog-user-result-lesson .el-dialog__body {
        padding: 0 0 15px 0;
    }

    .dialog-user-result-lesson .el-dialog__body {
        /*height: 100%;*/
    }

    .dialog-user-result-lesson .el-dialog {
        background: #F4F5FA;
        margin-top: 10vh !important;
        /*max-height: 80vh;*/
    }

    .dialog-user-instruction .el-dialog {
        background-image: url("{{ asset('/images/lessons/svg/bg-dialog-user-instruction.svg') }}");
        margin-top: 5vh !important;
    }

    .dialog-user-result-lesson.el-dialog__wrapper {
        overflow: unset;
    }

    /* Hiệu ứng chuyển đổi */
    @keyframes slide-in {
        0% {
            transform: translateX(-5rem);
            opacity: 0;
        }

        100% {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slide-out {
        0% {
            transform: translateX(0);
            opacity: 1;
        }

        100% {
            transform: translateX(5rem);
            opacity: 0;
        }
    }

    /* Áp dụng animation */
    .slide-in {
        animation: slide-in 0.5s forwards;
    }

    .slide-out {
        animation: slide-out 0.5s forwards;
    }

    #downloadApp {
        box-shadow: 0 13px 27.6px 0 #EC6E2366;
    }

    .dialog-user-result-lesson .el-dialog, .dialog-user-instruction .el-dialog {
        border-radius: 32px;      /* Bo viền */
        max-width: 1224px !important;
    }

    [v-cloak] {
        display: none;
    }

    u {
        text-underline-offset: 5px;
    }

    .vjs-poster img {
        object-fit: cover !important;
    }

    #content_explain img {
        max-width: 300px;
    }

    #explanationPopup img {
        height: 100% !important;
    }
    ruby {
        line-height: 2.2;
    }

    u {
        /*display: inline-block;*/
    }

    .run-text p {
        position: relative;
        /*white-space: nowrap;*/
        width: fit-content;
        color: #757575;
    }

    @keyframes run-text {
        from { width: 0 }
        to { width: 100% }
    }

    .slick-dots li {
        pointer-events: none; /* Ngăn click */
        /*opacity: 0.5; !* Làm mờ *!*/
    }

    .dot-container {
    /*    text-align: justify;*/
        position: relative;
    }

    .dot-container::after {
        content: "";
        display: block;
        width: 100%;
        height: 1px;
        background: repeating-linear-gradient(
                to right,
                #ffffff 0,
                #636363 1px,
                transparent 1px,
                transparent 3px
        );
        position: absolute;
        top: 50%;
    }

    .slide-dialog-instruction .slick-dots li.slick-active button:before {
        color: #57D061;
    }
</style>
