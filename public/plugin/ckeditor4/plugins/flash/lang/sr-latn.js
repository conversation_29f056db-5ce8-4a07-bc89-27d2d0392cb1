/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'flash', 'sr-latn', {
	access: '<PERSON><PERSON><PERSON> skripti',
	accessAlways: 'Uvek',
	accessNever: 'Nikada',
	accessSameDomain: 'Sa istog domena',
	alignAbsBottom: 'Abs dole',
	alignAbsMiddle: 'Abs sredina',
	alignBaseline: 'Bazno',
	alignTextTop: 'Vrh teksta',
	bgcolor: 'Boja pozadine',
	chkFull: 'Omogući ceo ekran',
	chkLoop: 'Neprekidno',
	chkMenu: 'Uključi fleš meni',
	chkPlay: 'Automatsko pokretanje',
	flashvars: 'Fleš varijable',
	hSpace: 'Vodorav.razdaljina',
	properties: 'Osobine fleša',
	propertiesTab: 'Osobine',
	quality: 'Kvalitet',
	qualityAutoHigh: 'Automatski dobro',
	qualityAutoLow: 'Automatski slabo',
	qualityBest: 'Najbolje',
	qualityHigh: 'Dobro',
	qualityLow: 'Slabo',
	qualityMedium: 'Srednje',
	scale: 'Merenje',
	scaleAll: 'Prikaži sve',
	scaleFit: 'Popuni sve',
	scaleNoBorder: 'Bez ivice',
	title: 'Osobine fleša',
	vSpace: 'Usprav. razdaljina',
	validateHSpace: 'U polje vodoravna razdaljina možete uneti samo brojeve.',
	validateSrc: 'Unesite URL linka',
	validateVSpace: 'U polje uspravna razdaljina možete uneti samo brojeve.',
	windowMode: 'Prozor mod',
	windowModeOpaque: 'Neprovidan',
	windowModeTransparent: 'Providan',
	windowModeWindow: 'Prozor'
} );
