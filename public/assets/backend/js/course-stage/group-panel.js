"use strict";function _typeof(e){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,o){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);o&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function _objectSpread(e){for(var o=1;o<arguments.length;o++){var t=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(t),!0).forEach(function(o){_defineProperty(e,o,t[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ownKeys(Object(t)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(t,o))})}return e}function _defineProperty(e,o,t){return o=_toPropertyKey(o),o in e?Object.defineProperty(e,o,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[o]=t,e}function _toPropertyKey(e){var o=_toPrimitive(e,"string");return"symbol"===_typeof(o)?o:String(o)}function _toPrimitive(e,o){if("object"!==_typeof(e)||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,o||"default");if("object"!==_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(e)}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,o){if(e){if("string"==typeof e)return _arrayLikeToArray(e,o);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(e,o):void 0}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,o){(null==o||o>e.length)&&(o=e.length);for(var t=0,r=new Array(o);t<o;t++)r[t]=e[t];return r}Vue.component("lesson-group-panel",{template:"#lesson-groups-panel-template",props:["category","permission"],data:function(){return{url:window.location.origin,groups:[],lessons:[],choseLessons:[],choseType:void 0,groupIdToChange:void 0,categories:[],showingGroup:{},loading:!1,showModal:!1,showLessonsModal:!1,draggable:!1,playbackRate:[.75,1,1.25,1.5,2],formData:{id:void 0,course_id:0,name:"",lesson_category_id:0,show:0,is_specialezed:0}}},watch:{category:function(e){var o=this;o.getGroupByCategory(e)}},mounted:function(){},methods:{printLessonIcon:function(e){var o="";switch(e){case"docs":o="docb.png";break;case"video":o="videob.png";break;case"test":o="quizb.png";break;case"flashcard":o="fcb.png";break;default:o="docb.png"}return o},changeLessonSpeed:function(e,o){var t=this,r={id:o.id,speed:e.target.value};$.post(t.url+"/backend/lesson-groups/change-lesson-speed",r,function(e){200===e.code?t.lessons=t.lessons.map(function(o){return o.id===e.data.id&&(o.default_speed=e.data.default_speed),o}):alert("Thất bại")})},changeLessonType:function(){var e=this;if(e.choseType&&e.choseLessons.length>0){var o={ids:_toConsumableArray(e.choseLessons),choseType:e.choseType};$.post(e.url+"/backend/lesson/change-lesson-type",o,function(o){200===o.code?(e.choseLessons=[],e.choseType=void 0,e.lessons=e.lessons.map(function(e,t){return e[t].type=o.data[t].type,e}),alert("Chuyển thành công")):alert("Chuyển thất bại")})}},changeGroupId:function(){var e=this;if(e.groupIdToChange&&e.choseLessons.length>0){var o={ids:_toConsumableArray(e.choseLessons),groupIdToChange:e.groupIdToChange};$.post(e.url+"/backend/lesson/change-group",o,function(o){200===o.code?(e.lessons=e.lessons.filter(function(o){return!e.choseLessons.includes(o.id)}),e.choseLessons=[],alert("Chuyển thành công")):alert("Chuyển thất bại")})}},checkAllLesson:function(e){var o=this,t=e.target.checked;t?o.choseLessons=o.lessons.map(function(e){return e.id}):o.choseLessons=[],console.log("array --> ",o.choseLessons)},checkOneLesson:function(e,o){var t=this,r=e.target.checked;r?t.choseLessons.push(o):t.choseLessons=t.choseLessons.filter(function(e){return e!==o}),console.log("array --> ",t.choseLessons)},toggleHideLessonTitle:function(e){var o=this,t={id:e.id};$.post(o.url+"/backend/lesson/toggle-secret",t,function(e){200===e.code?o.lessons=o.lessons.map(function(o){return o.id===e.data.id&&(o.is_secret=e.data.is_secret),o}):alert(e.msg)})},onChangeCheckbox:function(e,o){var t=this;t.formData[o]=e.target.checked?1:0},onDragLessonEnd:function(){var e=this,o=e.lessons.map(function(e){return e.id}),t={group_id:e.showingGroup.id,ids:o};$.post(e.url+"/backend/lesson-groups/apply-sorting-lesson",t,function(o){200===o.code&&(e.lessons=o.data.map(function(e){return e}))})},closeLessonsModal:function(){var e=this;e.lessons=[],e.showLessonsModal=!1,e.groupIdToChange=void 0,e.choseLessons=[]},showLessons:function(e){var o=this,t={id:e.id};o.showingGroup=e,$.post(o.url+"/backend/lesson-groups/get-lessons",t,function(e){200===e.code&&(o.lessons=_toConsumableArray(e.data),o.showLessonsModal=!0)})},onDragEnd:function(){var e=this,o=e.groups.map(function(e){return e.id}),t={course_id:e.category.course_id,lesson_category_id:e.category.id,ids:o};$.post(e.url+"/backend/lesson-groups/apply-sorting",t,function(o){200==o.code&&(e.groups=o.data.map(function(e){return e}))})},changeLessonStatus:function(e,o){var t=this,r={id:e.id,show:o};$.post(t.url+"/backend/lesson-groups/change-lesson-status",r,function(e){200===e.code&&(t.lessons=t.lessons.map(function(o){return o.id===e.data.id&&(o.show=e.data.show),o}))})},changeStatus:function(e,o){var t=this,r={id:e.id,show:o};$.post(t.url+"/backend/lesson-groups/change-status",r,function(e){200===e.code&&(t.groups=t.groups.map(function(o){return o.id===e.data.id&&(o.show=e.data.show),o}))})},saveForm:function(){var e=this,o=new FormData;o.append("id",e.formData.id),o.append("name",e.formData.name),o.append("course_id",e.category.course_id),o.append("lesson_category_id",e.formData.lesson_category_id),o.append("show",e.formData.show),e.formData.id?e.updateGroup(o):e.addGroup(o)},editGroup:function(e){var o=this;o.formData=_objectSpread(_objectSpread({},o.formData),{},{id:e.id,name:e.name,lesson_category_id:e.lesson_category_id,show:e.show}),o.showModal=!0},addGroup:function(e){var o=this;$.ajax({url:o.url+"/backend/lesson-groups/add-group",type:"POST",data:e,processData:!1,contentType:!1,success:function(e){200===e.code?(e.data.lesson_category_id===o.category.id&&o.groups.push(e.data),o.closeModal()):alert(e.msg)}})},updateGroup:function(e){var o=this;$.ajax({url:o.url+"/backend/lesson-groups/update-group",type:"POST",data:e,processData:!1,contentType:!1,success:function(e){200===e.code?(e.data.lesson_category_id!==o.category.id?o.groups=_.remove(o.groups,function(o){return o.id!==e.data.id}):o.groups=o.groups.map(function(o){return o.id===e.data.id&&(o=_objectSpread({},e.data)),o}),o.closeModal()):alert(e.msg)}})},deleteGroup:function(e){var o=this,t=window.confirm("Xác nhận xoá nhóm bài học cùng với toàn bộ bài học trong nhóm?");if(t){var r={id:e.id};$.post(o.url+"/backend/lesson-groups/delete-group",r,function(t){200===t.code&&(o.groups=o.groups.filter(function(o){return o.id!==e.id}))})}},setFormStatus:function(e){var o=this;o.formData.show=parseInt(e.target.value)},closeModal:function(){var e=this;e.formData=_objectSpread(_objectSpread({},e.formData),{},{id:void 0,course_id:0,name:"",lesson_category_id:0,show:0}),e.showModal=!1},getGroupByCategory:function(e){var o=this;o.loading=!0;var t={course_id:e.course_id,id:e.id};$.post(o.url+"/backend/lesson-groups",t,function(e){200===e.code&&(o.groups=_toConsumableArray(e.data.groups),o.categories=_toConsumableArray(e.data.categories),o.loading=!1)})}}});