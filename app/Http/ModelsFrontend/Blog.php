<?php

namespace App\Http\ModelsFrontend;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use DB;

class Blog extends Model{

    protected $table = 'blog';

//    protected $appends = ['category', 'author_name'];
    protected $appends = ['author_name'];

   public function getCategoryAttribute() {
       return "category";
   }

    public function getAuthorNameAttribute() {
        return "Dũng Mori";
    }

   // // Lấy ra categoryName
   // public function getCategoryName() {
   //     return $this->hasOne('App\Http\ModelsFrontend\CategoryBlog', 'id', 'category_id');
   // }


    public function getFriendlyUrl() {
        $string = new StringConvert();
        //url = url than thien + id
        $url = $string->sanitizeTitle($this->title);
        return $url;
    }

    public function getDate() {
        $thisDateTime = $this->created_at;
        $compareOnDay = substr($this->created_at, 0, 10);

        if (date('Y-m-d') == $compareOnDay) {
            // Nếu là trong ngày
            return "Hôm nay";
        } else {
            // Nếu không phải trong ngày
            $extraYear = substr($this->created_at, 0, 4);
            return substr($thisDateTime, 8, 2) . "/". substr($thisDateTime, 5, 2) . "/" . $extraYear;
        }
    }

    // Lấy ra ngày giờ ở dạng thân thiện
    public function getFriendlyTime() {
        $thisDateTime = $this->created_at;
        $compareOnDay = substr($this->created_at, 0, 10);

        if (date('Y-m-d') == $compareOnDay) {
            // Nếu là trong ngày
            return "Hôm nay, lúc ". substr($thisDateTime, 11, 5);
        } else {
            // Nếu không phải trong ngày

            $extraYear = substr($this->created_at, 0, 4);
            $thisYear = date("Y");

            //nếu là trong năm
            if ($extraYear == $thisYear) {
                return substr($thisDateTime, 8, 2)." tháng ". substr($thisDateTime, 5, 2).", lúc ". substr($thisDateTime, 11, 5);
            } else {
                return substr($thisDateTime, 8, 2)." tháng ". substr($thisDateTime, 5, 2).", ".$extraYear;
            }
        }
    }
}
