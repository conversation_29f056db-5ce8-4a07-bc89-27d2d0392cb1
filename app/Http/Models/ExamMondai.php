<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class ExamMondai extends Model
{
  protected $connection = 'mysql';
  protected $table = 'exam_mondais';
    protected $casts = [
        'title' => 'integer',
    ];
  public function questions()
  {
      return $this->hasMany(\App\Http\ModelsFrontend\LessonComponents::class, 'mondai_id')->whereIn('type', [3, 13])->orderBy('sort');
  }

  public function tasks()
  {
      return $this->hasMany(\App\Http\ModelsFrontend\LessonComponents::class, 'mondai_id')->orderBy('sort');
  }
}
