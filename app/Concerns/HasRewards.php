<?php

namespace App\Concerns;

use App\Enums\RewardType;
use App\Models\Activity;
use App\Models\Pivots\RewardAudit;
use App\Models\Reward;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use function Symfony\Component\String\s;

trait HasRewards
{
  public function rewards(): BelongsToMany
  {
    return $this->belongsToMany(related: Reward::class, table: 'reward_audits')
      ->withTimestamps()
      ->withPivot('claim_at', 'triggered_at', 'end_at', 'coupon_id', 'id')
      ->using(RewardAudit::class);
  }

  public function availableDiscountRewards(): BelongsToMany
  {
    return $this->belongsToMany(related: Reward::class, table: 'reward_audits')
      ->withTimestamps()
      ->withPivot('claim_at', 'triggered_at', 'end_at', 'coupon_id', 'id')
      ->whereIn('type', ['discount_money', 'discount_percent'])
      ->whereNull('claim_at')
      ->whereNull('end_at')
      ->using(RewardAudit::class);
  }

  public function activeMultiplierRewards(): BelongsToMany
  {
    return $this->belongsToMany(related: Reward::class, table: 'reward_audits')
      ->withTimestamps()
      ->withPivot('claim_at', 'triggered_at', 'end_at', 'coupon_id', 'id')
      ->where('type', 'multiplier')
      ->whereNotNull('claim_at')
      ->whereNotNull('end_at')
      ->where('claim_at', '<=', now())
      ->where('end_at', '>=', now())
      ->using(RewardAudit::class);
  }
  /**
   * @param Reward $reward
   * @return false|mixed
   */
  public function grantReward(Reward $reward)
  {
    /** trường hợp bảo vệ chuỗi
     * claim_at mốc thời gian user bấm sử dụng
     * triggered_at mốc thời gian phần thưởng được kích hoạt
     * bảo vệ cho chuỗi gần nhất hiện tại
     * nếu streak activity_at khác yesterday, kích hoạt bảo vệ chuỗi, sửa activity_at thành tomorrow của nó, triggered_at là activity_at sau khi sửa
     * nếu đã bảo vệ chuỗi mà vẫn quá chuỗi, thì reset, trong histories lưu count + 1 và ended_at là triggered_at
     *
    **/

    /** trường hợp nhân điểm
     *  claim_at là thời gian bắt đầu user ấn sử dụng, tương đương với thời gian bắt đầu tính hiệu lực nhân điểm
     *  end_at mốc thời gian phần thưởng hết hiệu lực
     **/
    $preventSpamReward = RewardAudit::query()->where('user_id', $this->id)->where('created_at', '>=', Carbon::now()->subSeconds(10))->exists();
    if ($preventSpamReward) return false;
    $experience = $this->deductPoints(amount: $reward->price, reason: 'Đổi ' . $reward->price . ' lấy item ' . $reward->name, reasonTable: 'rewards', reasonId: $reward->id);
    $this->rewards()->attach($reward);
    return $experience->experience_points;
  }

  /**
   * @param RewardAudit $audit
   * @return bool
   */
  public function activeReward(RewardAudit $audit)
  {
    $reward = Reward::find($audit->reward_id);
    switch ($reward->type) {
      case RewardType::Point->value:
        $this->addPoints($reward->value);
        $audit->claim_at = now();
        $audit->save();
        break;
      case RewardType::Multiplier->value:
        $now = Carbon::now();
        $audit->claim_at = $now;
        $audit->end_at = $now->copy()->addHours($reward->expired_in);
        $audit->save();
        break;
      case RewardType::CourseTime->value:
        $activeCourse = $this->getActiveCourseOwner;
        $this->extendUserCourseTime($activeCourse, (int) $reward->value);
        $audit->claim_at = now();
        $audit->save();
        break;
      case RewardType::StreakFreeze->value:
        $now = Carbon::now();
        $activity = Activity::find(1);
        $this->freezeStreak($activity, (int) $reward->value + 1);
        $audit->claim_at = now();
        $audit->end_at = $now->copy()->addDays($reward->value + 1)->startOfDay();
        $audit->save();
        break;
      case RewardType::DiscountMoney->value:
      case RewardType::DiscountPercent->value:
      case RewardType::Good->value:
        break;
      default:
        echo("dc default");
        break;
    }

    return true;
  }
}
