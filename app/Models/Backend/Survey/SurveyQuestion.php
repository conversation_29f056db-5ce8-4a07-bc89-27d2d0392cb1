<?php

namespace App\Models\Backend\Survey;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SurveyQuestion extends Model
{
    use HasFactory;
    public $timestamps = false;

    protected $table = 'survey_questions';
    protected $fillable = [
        'survey_id',
        'question'
    ];

    public function answer(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(SurveyAnswer::class,'question_id', 'id');
    }
}
