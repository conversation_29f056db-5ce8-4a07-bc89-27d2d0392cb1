"use strict";Vue.component("modal",{template:"#modal-template"});
"use strict";$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),Vue.use(CKEditor),Vue.component("task-content-form",{template:"#task-content-form-template",props:["lesson_id","type","currentTask"],data:function(){return{url:window.location.origin,task:{},show:1,is_quiz:0,value:"",editorConfig:{minHeight:"300px",filebrowserBrowseUrl:this.url+"/backend/ckfinder/browser",filebrowserUploadUrl:this.url+"/backend/ckfinder/connector?command=QuickUpload&type=Files",extraPlugins:["custom","maximize","sourcearea","button","panelbutton","fakeobjects","justify","colorbutton","dialogui","dialog","filetools","popup","filebrowser","font","table","image","furigana","panel","listblock","floatpanel","richcombo","format"],allowedContent:!0}}},mounted:function(){var t=this;t.task=t.currentTask,t.show=t.task.hasOwnProperty("show")?t.task.show:1,t.is_quiz=t.task.hasOwnProperty("is_quiz")?t.task.is_quiz:0,t.value=t.task.value?t.task.value:""},methods:{onSaveTask:function(){var t=this,e={id:t.currentTask.id,lesson_id:t.lesson_id,type:t.type,show:t.show?1:0,is_quiz:t.is_quiz?1:0,value:t.value};e.id?t.onUpdateTask(e):t.onAddNewTask(e)},onAddNewTask:function(t){var e=this;$.post(e.url+"/backend/new-lesson/content/add",t,function(t){200==t.code&&(e.$emit("addedTask",t.data),toastr.success("Thay đổi thành công!!"))})},onUpdateTask:function(t){var e=this;$.ajax({url:e.url+"/backend/new-lesson/content/update",type:"PUT",data:t,success:function(t){200==t.code&&(e.$emit("updatedTask",t.data),toastr.success("Thay đổi thành công!!"))}})},cancel:function(){var t=this;t.$emit("closeModal")}}});
"use strict";$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),Vue.use(CKEditor),Vue.component("task-quiz-form",{template:"#task-quiz-form-template",props:["lesson_id","type","currentTask"],data:function(){return{url:window.location.origin,task:{},show:1,value:"",grade:0,answers:[{id:void 0,sort:1,value:"",grade:0},{id:void 0,sort:2,value:"",grade:0}],checked:void 0,editorConfig:{minHeight:"300px",filebrowserBrowseUrl:this.url+"/backend/ckfinder/browser",filebrowserUploadUrl:this.url+"/backend/ckfinder/connector?command=QuickUpload&type=Files",extraPlugins:["maximize","sourcearea","button","panelbutton","fakeobjects","justify","colorbutton","dialogui","dialog","filetools","popup","filebrowser","font","table","image","furigana","panel","listblock","floatpanel","richcombo","format"],allowedContent:!0,enterMode:CKEDITOR.ENTER_BR}}},mounted:function(){var e=this;e.task=e.currentTask,e.show=e.task.hasOwnProperty("show")?e.task.show:1,e.value=e.task.value?e.task.value:"",e.grade=e.task.grade?e.task.grade:"",e.currentTask.id&&e.getAnswersByTask()},methods:{onSaveTask:function(){var e=this,a={id:e.currentTask.id,lesson_id:e.lesson_id,type:e.type,show:e.show?1:0,value:e.value,grade:parseInt(e.grade),answers:e.answers},r=e.validate();r&&(a.id?e.onUpdateTask(a):e.onAddNewTask(a))},onAddNewTask:function(e){var a=this;$.post(a.url+"/backend/new-lesson/quiz/add",e,function(e){200==e.code&&(a.$emit("addedTask",e.data),toastr.success("Thay đổi thành công!!"))})},onUpdateTask:function(e){var a=this;$.ajax({url:a.url+"/backend/new-lesson/quiz/update",type:"PUT",data:e,success:function(e){200==e.code&&(a.$emit("updatedTask",e.data),toastr.success("Thay đổi thành công!!"))}})},cancel:function(){var e=this;e.$emit("closeModal")},addAnswers:function(){var e=this,a=e.answers,r=e.answers.map(function(e){return e.sort}),t=a.length>0?_.max(r):0;a.push({id:void 0,sort:t+1,value:void 0,grade:0}),e.answers=a},checkedAnswer:function(){var e=this;e.answers=e.answers.map(function(a){return a.grade=a.sort==e.checked?parseInt(e.grade):0,a})},removeRow:function(e){var a=this;a.answers=a.answers.filter(function(a){return a.sort!=e.sort});for(var r=0;r<a.answers.length;r++)a.answers[r].sort=r+1},validate:function(){var e=this;if(_.isNil(e.value)||""==e.value)return toastr.error("Không bỏ trống nội dung câu hỏi"),!1;if(_.isNil(e.grade)||0==e.grade)return toastr.error("Điểm của bài quiz phải lớn hơn 0"),!1;for(var a=0,r=0;r<e.answers.length;r++)if(a+=parseInt(e.answers[r].grade),_.isNil(e.answers[r].value)||""==e.answers[r].value)return toastr.error("Không bỏ trống nội dung câu trả lời"),!1;return 0!=a||(toastr.error("Cần có câu trả lời đúng"),!1)},getAnswersByTask:function(){var e=this,a=e.currentTask.id;$.post(e.url+"/backend/new-lesson/quiz/get-answers-by-task",{id:a},function(a){200==a.code&&(e.answers=a.data.map(function(a){return parseInt(a.grade)>0&&(e.checked=a.sort),{id:a.id,sort:a.sort,value:a.value,grade:parseInt(a.grade)}}))})}}});
"use strict";$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),Vue.use(CKEditor),Vue.component("task-gap-fill-form",{template:"#task-gap-fill-form-template",props:["lesson_id","type","currentTask"],data:function(){return{url:window.location.origin,task:{},show:1,value:"",grade:0,answers:[],editorConfig:{minHeight:"300px",filebrowserBrowseUrl:this.url+"/backend/ckfinder/browser",filebrowserUploadUrl:this.url+"/backend/ckfinder/connector?command=QuickUpload&type=Files",extraPlugins:["maximize","sourcearea","button","panelbutton","fakeobjects","justify","colorbutton","dialogui","dialog","filetools","popup","filebrowser","font","table","image","furigana","panel","listblock","floatpanel","richcombo","format"],allowedContent:!0,enterMode:CKEDITOR.ENTER_BR}}},watch:{value:{deep:!0,handler:function(e){var a=this;a.checkHide(e)}}},mounted:function(){var e=this;e.task=e.currentTask,e.show=e.task.hasOwnProperty("show")?e.task.show:1,e.value=e.task.value?e.task.value:"",e.grade=e.task.grade?e.task.grade:"",e.currentTask.id&&e.getAnswersByTask()},methods:{checkHide:function(e){var a=this,t=/\[\[([^[]+)\]\]/g,s=/\[\*([^[]+)\*\]/g,r=0,o=[];do m=t.exec(e),m&&(o[r]={id:void 0,value:m[1],sort:m.index,grade:0},r++);while(m);do n=s.exec(e),n&&(o[r]={id:void 0,value:n[1],sort:n.index,grade:a.grade},r++);while(n);o=_.orderBy(o,"sort"),a.answers=o.map(function(e,t){return a.answers[t]&&a.answers[t].id?e.id=a.answers[t].id:e.id=void 0,e})},onSaveTask:function(){var e=this,a={id:e.currentTask.id,lesson_id:e.lesson_id,type:e.type,show:e.show?1:0,value:e.value,grade:parseInt(e.grade),answers:e.answers},t=e.validate();t&&(a.id?e.onUpdateTask(a):e.onAddNewTask(a))},onAddNewTask:function(e){var a=this;$.post(a.url+"/backend/new-lesson/gap-fill/add",e,function(e){if(200==e.code){var t=e.data;t.type=parseInt(e.data.type),t.show=parseInt(e.data.show),a.$emit("addedTask",e.data),toastr.success("Thay đổi thành công!!")}})},onUpdateTask:function(e){var a=this;$.ajax({url:a.url+"/backend/new-lesson/gap-fill/update",type:"PUT",data:e,success:function(e){if(200==e.code){var t=e.data;t.type=parseInt(e.data.type),t.show=parseInt(e.data.show),a.$emit("updatedTask",t),toastr.success("Thay đổi thành công!!")}}})},cancel:function(){var e=this;e.$emit("closeModal")},validate:function(){var e=this;return _.isNil(e.value)||""==e.value?(toastr.error("Không bỏ trống nội dung câu hỏi"),!1):!_.isNil(e.grade)&&0!=e.grade||(toastr.error("Điểm của bài quiz phải lớn hơn 0"),!1)},getAnswersByTask:function(){var e=this,a=e.currentTask.id;$.post(e.url+"/backend/new-lesson/quiz/get-answers-by-task",{id:a},function(a){200==a.code&&(e.answers=a.data.map(function(e){return{id:e.id,sort:e.sort,value:e.value,grade:e.grade}}))})}}});
"use strict";$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),Vue.use(CKEditor),Vue.component("task-interactive-video-form",{template:"#task-interactive-video-form-template",props:["lesson_id","type","currentTask"],data:function(){return{url:window.location.origin,task:{},show:0,video_title:"",video_name:"",editorConfig:{minHeight:"300px",filebrowserBrowseUrl:this.url+"/backend/ckfinder/browser",filebrowserUploadUrl:this.url+"/backend/ckfinder/connector?command=QuickUpload&type=Files",extraPlugins:["custom","maximize","sourcearea","button","panelbutton","fakeobjects","justify","colorbutton","dialogui","dialog","filetools","popup","filebrowser","font","table","image","furigana","panel","listblock","floatpanel","richcombo","format"],allowedContent:!0}}},mounted:function(){var e=this;e.task=e.currentTask,e.show=e.task.show?e.task.show:0,e.video_title=e.task.video_title?e.task.video_title:"",e.video_name=e.task.video_name?e.task.video_name:""},methods:{onSaveTask:function(){var e=this,t={id:e.currentTask.id,lesson_id:e.lesson_id,type:e.type,show:e.show?1:0,video_name:e.video_name,video_title:e.video_title};t.id?e.onUpdateTask(t):e.onAddNewTask(t)},onAddNewTask:function(e){var t=this;$.post(t.url+"/backend/new-lesson/interactive-video/add",e,function(e){200==e.code&&(t.$emit("addedTask",e.data),toastr.success("Thay đổi thành công!!"))})},onUpdateTask:function(e){var t=this;$.ajax({url:t.url+"/backend/new-lesson/interactive-video/update",type:"PUT",data:e,success:function(e){200==e.code&&(t.$emit("updatedTask",e.data),toastr.success("Thay đổi thành công!!"))}})},cancel:function(){var e=this;e.$emit("closeModal")}}});
"use strict";$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),Vue.component("lesson-info",{template:"#lesson-info-template",props:["lesson","groups","courses","authors"],data:function(){var e=this;return{lessonGroups:e.groups,name:_.get(e.lesson,"name",""),show:_.get(e.lesson,"show",0),author:_.get(e.lesson,"get_author.id",""),course:_.get(e.lesson,"get_course.id",""),group_id:_.get(e.lesson,"group_id",""),price_option:_.get(e.lesson,"price_option",0),feature:_.get(e.lesson,"feature",0),avatar_name:_.get(e.lesson,"avatar_name",""),avatarPreview:""}},watch:{status:function(e){this.status=e?1:0},course:function(e){var o=this;o.getGroupsByCourseId(e)}},mounted:function(){},methods:{readURL:function(e){var o=this;if(e.target.files&&e.target.files[0]){var t=new FileReader;t.onload=function(e){o.avatarPreview=e.target.result},t.readAsDataURL(e.target.files[0])}},submit:function(){var e=this,o=new FormData($("#lesson-info-form")[0]);o.append("id",e.lesson.id),o.append("lesson",e.name),o.append("course",e.course),o.append("group",e.group_id),o.append("feature",e.feature),o.append("status",e.show),o.append("teacher",e.author),o.append("price",e.price_option),$.ajax({url:window.location.origin+"/backend/new-lesson/update",type:"post",processData:!1,contentType:!1,data:o,success:function(o){window.location.href=window.location.origin+"/backend/lesson/filter/"+e.course+"/"+e.group_id}})},getGroupsByCourseId:function(e){var o=this,t={course_id:e};$.post(window.location.origin+"/backend/group-by-course",t,function(e){o.lessonGroups=e.group,e.group.length>0&&(o.group_id=e.group[0].id)})}}});
"use strict";$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),Vue.filter("show",function(s){switch(s){case 0:return"Tắt";case 1:return"Bật";case 2:return"Testing";default:return"Tắt"}}),Vue.filter("taskType",function(s){switch(s){case 1:return"Nội dung";case 2:return"Mp4/Youtube";case 3:return"Trắc nghiệm";case 4:return"Kết quả";case 5:return"Mp3";case 6:return"Trả lời câu hỏi";case 7:return"Kaiwa";case 8:return"Tài liệu PDF";case 9:return"Flashcard";case 10:return"Quiz trắc nghiệm";case 11:return"Sắp xếp câu";case 12:return"Video tương tác";default:return"Nội dung"}}),Vue.component("lesson-tasks",{template:"#lesson-tasks-template",props:["lesson","gradeByTask"],data:function(){var s=this;return{url:window.location.origin,tasks:[],currentTask:{},is_examination:s.lesson.is_examination,total_marks:s.lesson.total_marks,pass_marks:s.lesson.pass_marks,type:1,detailLesson:s.lesson,showModal:!1,draggable:!0}},watch:{},mounted:function(){var s=this;s.setToastrTimeout(),s.getComponentsByLesson()},methods:{closeModal:function(){var s=this;s.currentTask={},setTimeout(function(){s.showModal=!1},100)},setToastrTimeout:function(){toastr.options.timeOut=2e3,toastr.options.extendedTimeOut=2e3},renderPoint:function(s){return 0==s.grade?"<span></span>":s.match_grade?"<span class='text-success font-bold'>"+s.grade+"</span>":"<span class='text-danger font-bold'>"+s.grade+"</span>"},getChipColor:function(s){switch(s){case 0:return"#eb3434";case 1:return"#2d973d";case 2:return"#4c57eb";default:return"#eb3434"}},getComponentsByLesson:function(){var s=this,t={id:s.lesson.id};$.post(s.url+"/backend/new-lesson/components-by-lesson",t,function(t){200==t.code&&(s.tasks=t.data.map(function(s){return 4===s.type&&console.log(JSON.parse(s.value)),s}))})},save:function(){var s={lesson_id:vm.detailLesson.id,is_examination:vm.is_examination?1:0,total_marks:vm.total_marks,pass_marks:vm.pass_marks};$.post(vm.url+"/backend/new-lesson/update/content",s,function(s){200==s.code&&toastr.success("Thay đổi thành công!!")})},cancel:function(){var s=this,t=_.get(s.detailLesson,"get_course.id",""),e=_.get(s.detailLesson,"group_id",""),n=s.is_examination!=s.lesson.is_examination||s.total_marks!=s.lesson.total_marks||s.pass_marks!=s.lesson.pass_marks;if(n){var a=window.confirm("Xác nhận chuyển trang. Tất cả thay đổi sẽ không được lưu lại");a&&(window.location.href=s.url+"/backend/lesson/filter/"+t+"/"+e)}else window.location.href=s.url+"/backend/lesson/filter/"+t+"/"+e},deleteTask:function(s){var t=this,e=window.confirm("Xác nhận xoá tác vụ?");if(e){var n={id:s};$.post(t.url+"/backend/new-lesson/delete-component",n,function(s){200==s.code&&(t.tasks=t.tasks.filter(function(t){return t.id!=s.data.id}),toastr.success("Đã xoá tác vụ #"+s.data.id))})}},onDragEnd:function(){var s=this,t=s.tasks.map(function(s){return s.id}),e={lesson_id:s.detailLesson.id,ids:t};$.post(s.url+"/backend/new-lesson/apply-sorting",e,function(t){200==t.code&&(s.tasks=t.data.map(function(s){return s}))})},editTask:function(s){var t=this;t.type=s.type,t.currentTask=s,t.showModal=!0},pushAddedTask:function(s){var t=this;s.show=parseInt(s.show),t.tasks.push(s),t.closeModal()},updateTask:function(s){var t=this;s.show=parseInt(s.show),t.tasks=t.tasks.map(function(t){return t.id==s.id&&(t=s),t}),t.closeModal()}}});
"use strict";var newLesson=new Vue({el:"#new-lesson-detail-screen",data:function(){return{url:window.location.origin,activeTab:"tasks",tabs:[{value:"info",name:"Thông tin"},{value:"tasks",name:"Nội dung"}],lessonId:0}},methods:{convertToQuiz:function(){var n=this,e={lessonId:n.lessonId};$.post(n.url+"/backend/new-lesson/convert-to-quiz",e,function(n){200===n.code&&alert("Chuyển đổi thành công. F5 để thấy kết quả")})}},mounted:function(){var n=this;n.lessonId=lesson.id}});