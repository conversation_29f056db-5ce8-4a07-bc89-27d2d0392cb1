Make sure these boxes are checked before submitting your issue -- thank you!

- [ ] Check [FAQ](https://github.com/mdbootstrap/perfect-scrollbar/wiki/FAQ) and [Caveats](https://github.com/mdbootstrap/perfect-scrollbar/wiki/Caveats)
- [ ] Search if there's already one reported in Issues
- [ ] Prepare a JSFiddle reproducing the issue
  - [perfect-scrollbar JSFiddle](https://jsfiddle.net/utatti/dyvL31r6/)
- [ ] Provide a page or source code where the issue can be checked
