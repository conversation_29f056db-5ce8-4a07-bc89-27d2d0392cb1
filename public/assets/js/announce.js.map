{"version": 3, "sources": ["announce.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "announce.js", "sourcesContent": ["// Huỷ đóng popup thông báo khi người dùng click vào popup\n$(document).on('click', '.dropdown-menu', function (e) {\n    e.stopPropagation();\n});\n\nimport Vue from 'vue';\nimport UserAnnouce from '../component/UserAnnouce';\n\nVue.component('user-annouce', UserAnnouce);\n\nvar announce = new Vue({\n    el: '#announce',\n});\n\nannounce.$mount('#announce');"]}