<?php

namespace App\Repositories\School;

use App\Base\Repository\EloquentRepository;
use App\Http\Models\School\SchoolUser;

class SchoolUserRepository extends EloquentRepository
{
    public function __construct(SchoolUser $model)
    {
        parent::__construct($model);
    }

    public function getList($page, $perPage, $filters, $timeFilters, $nameFilter, $vipDepartment, $teacherType)
    {
        foreach ($filters as $field => $value) {
            if (is_array($value)) {
                list($field, $condition, $val) = $value;
                $this->model = $this->model->where($field, $condition, $val);
            } else {
                $this->model = $this->model->where($field, '=', $value);
            }
        }
        if ($nameFilter) {
            $this->model = $this->model->whereRaw("CONCAT(last_name, ' ', first_name) LIKE '%$nameFilter%'");
        }
        switch ($timeFilters['time_frame']) {
            case 'monthly':
                $startDate = $timeFilters['current_time']->copy()->startOfMonth()->startOfDay();
                $endDate = $timeFilters['current_time']->copy()->endOfMonth()->endOfDay();
                $type = 2;
                break;
            case '26,25':
            default:
                $startDate = $timeFilters['current_time']->copy()->subMonth()->startOfMonth()->addDays(25)->startOfDay();
                $endDate = $timeFilters['current_time']->copy()->startOfMonth()->addDays(24)->endOfDay();
                $type = 1;
                break;
        }

        $startYear = $timeFilters['current_time']->copy()->startOfYear();
        $endYear = $timeFilters['current_time']->copy()->endOfYear();

        $query = $this->model
            ->select('id', 'dmr_id', 'user_name', 'email', 'department_id', 'role', 'first_name', 'last_name', 'note', 'note_by_accountant', 'join_date', 'status', 'tax_id')
            ->whereIn('department_id', $vipDepartment);

            if ($teacherType == 'online') {
                $query = $query->with(['latest_salaries' => function ($q) use ($timeFilters, $startDate, $endDate, $type) {
                    $q->select('id', 'base_salary', 'currency', 'group_id', 'level', 'type', 'user_id', 'valid_from')
                        ->with('group:id,name')->whereNull('deleted_at')
                        ->where('valid_from', '<=', $endDate)
                        ->where('type', $type)
                        ->whereNotNull('group_id')
                        ->orderBy('valid_from', 'desc')
                        ->orderBy('id', 'desc')
                        ->groupBy('group_id');
                }]);
            } else {
                $query = $query->with(['latest_salaries' => function ($q) use ($timeFilters, $startDate, $endDate, $type) {
                    $q->select('id', 'base_salary', 'currency', 'group_id', 'course_id', 'level', 'type', 'user_id', 'valid_from')
                        ->with('group:id,name')
                        ->with(['course' => function ($q) {
                            $q->with(['info' => function ($q) {
                                $q->with('periods:day_times_mst.int,period_code')->with('room:id,room_name');
                            }])->with('department:department.id,name');
                        }])
                        ->where('valid_from', '<=', $endDate)
                        ->where('type', $type)
                        ->whereNull('deleted_at')
                        ->whereNotNull('course_id')
                        ->orderBy('valid_from', 'desc')
                        ->orderBy('id', 'desc')
                        ->groupBy('group_id');
                }]);
            }

            if ($teacherType == 'online') {
                $query = $query->with(['salaries' => function ($q) use ($timeFilters, $startDate, $endDate, $type) {
                    $q->select('id', 'base_salary', 'currency', 'group_id', 'course_id', 'level', 'type', 'user_id', 'valid_from')
                        ->with('group:id,name')
                        ->whereNull('deleted_at')
                        ->where('valid_from', '<=', $endDate)
                        ->where('type', $type)
                        ->whereNotNull('group_id')
                        ->orderBy('level')
                        ->orderBy('valid_from')
                        ->orderBy('id', 'desc');
                }]);
            } else {
                $query = $query->with(['salaries' => function ($q) use ($timeFilters, $startDate, $endDate, $type) {
                    $q->select('id', 'base_salary', 'currency', 'group_id', 'course_id', 'level', 'type', 'user_id', 'valid_from')
                        ->with('group:id,name')
                        ->with(['course' => function ($q) {
                            $q->with(['info' => function ($q) {
                                $q->with('periods:int,period_code')->with('room:id,room_name');
                            }])->with('department:department.id,name');
                        }])
                        ->whereNull('deleted_at')
                        ->where('valid_from', '<=', $endDate)
                        ->where('type', $type)
                        ->whereNotNull('course_id')
                        ->orderBy('level')
                        ->orderBy('valid_from')
                        ->orderBy('id', 'desc');
                }]);
            }

            $query = $query->with(['groups' => function ($gr) use ($startYear, $endYear) {
                $gr->select('id', 'course_id', 'group_id', 'teacher_id', 'deleted')->with(['group' => function ($q) use ($startYear, $endYear) {
                    $q->select('id', 'name', 'start_date', 'deleted_at')->where('start_date', '>=', $startYear)
                        ->where('start_date', '<=', $endYear)
                        ->whereNull('deleted_at');
                }])->where('deleted', 0)->whereNotNull('group_id');
            }]);

            $query = $query->with('coursest.info');
            if ($teacherType == 'online') {
                $query = $query->with(['work_days' => function ($q) use ($timeFilters, $startDate, $endDate) {
                    $q->select('id', 'course_id', 'group_id', 'teacher_id', 'index', 'date_attendance', 'status', 'deleted', 'ref_reward' ,'reward' ,'fault')
                        ->with(['group' => function ($q1) {
                            $q1->select('id', 'name')->with('group_report:id,teacher_id,course_id,group_id');
                        }])
                        ->whereNotNull('group_id')
                        ->where('status', 0)
                        ->where('deleted', 0)
                        ->whereDate('date_attendance', '>=', $startDate)
                        ->whereDate('date_attendance', '<=', $endDate);
                }]);
            } else {
                $query = $query->with(['work_days' => function ($q) use ($timeFilters, $startDate, $endDate) {
                    $q->select('id', 'course_id', 'group_id', 'teacher_id', 'index', 'date_attendance', 'status', 'deleted', 'ref_reward' ,'reward' ,'fault')
                        ->with(['group' => function ($q1) {
                            $q1->select('id', 'name')->with('group_report:id,teacher_id,course_id,group_id');
                        }])
                        ->with(['course' => function ($q) {
                            $q->with(['info' => function ($q) {
                                $q->with('periods:int,period_code')->with('room:id,room_name');
                            }])->with('department:department.id,name');
                        }])
                        ->whereNotNull('course_id')
                        ->where('status', 0)
                        ->where('deleted', 0)
                        ->whereDate('date_attendance', '>=', $startDate)
                        ->whereDate('date_attendance', '<=', $endDate);
                }]);
            }

            $query = $query->with('tax');

            if ($teacherType == 'online') {
                $query = $query->withCount(['salaries' => function ($q) use ($timeFilters, $startDate, $endDate, $type) {
                    $q->with('group:id,name')->with('courset')
                        ->whereNull('deleted_at')
                        ->where('valid_from', '<=', $endDate)
                        ->where('type', $type)
                        ->whereNotNull('group_id')
                        ->orderBy('level')
                        ->orderBy('valid_from')
                        ->orderBy('id', 'desc');
                }]);
            } else {
                $query = $query->withCount(['salaries' => function ($q) use ($timeFilters, $startDate, $endDate, $type) {
                    $q->with('group:id,name')->with('courset')
                        ->whereNull('deleted_at')
                        ->where('valid_from', '<=', $endDate)
                        ->where('type', $type)
                        ->whereNotNull('course_id')
                        ->orderBy('level')
                        ->orderBy('valid_from')
                        ->orderBy('id', 'desc');
                }]);
            }

        if ($teacherType == 'online') {
            return $query->where('deleted', 0)
                ->havingRaw('salaries_count > 0')
                ->paginate($perPage, ['*'], 'page', $page);
        } else {
            return $query->where('deleted', 0)
                ->paginate($perPage, ['*'], 'page', $page);
        }
    }

    public function getUserMonthSalary($id, $timeFilters)
    {
        switch ($timeFilters['time_frame']) {
            case 'monthly':
                $startDate = $timeFilters['current_time']->copy()->startOfMonth()->startOfDay();
                $endDate = $timeFilters['current_time']->copy()->endOfMonth()->endOfDay();
                $type = 2;
                break;
            case '26,25':
            default:
                $startDate = $timeFilters['current_time']->copy()->subMonth()->startOfMonth()->addDays(25)->startOfDay();
                $endDate = $timeFilters['current_time']->copy()->startOfMonth()->addDays(24)->endOfDay();
                $type = 1;
                break;
        }
        $startYear = $timeFilters['current_time']->copy()->startOfYear();
        $endYear = $timeFilters['current_time']->copy()->endOfYear();

        return $this->model
            ->select('id', 'dmr_id', 'user_name', 'email', 'department_id', 'role', 'first_name', 'last_name', 'note', 'note_by_accountant', 'join_date', 'status', 'tax_id')
            ->with(['latest_salaries' => function ($q) use ($timeFilters, $startDate, $endDate, $type) {
                $q->with('group')->whereNull('deleted_at')
                    ->where('valid_from', '<=', $endDate)
                    ->where('type', $type)
                    ->orderBy('valid_from', 'desc')
                    ->orderBy('id', 'desc')
                    ->groupBy('group_id');
            }])
            ->with(['salaries' => function ($q) use ($timeFilters, $startDate, $endDate, $type) {
                $q->with('group')->whereNull('deleted_at')
                    ->where('type', $type)
                    ->orderBy('level')
                    ->orderBy('valid_from')
                    ->orderBy('id', 'desc');
            }])
            ->with(['work_days' => function ($q) use ($timeFilters, $startDate, $endDate) {
                $q->with('group')
                    ->where('status', 0)
                    ->where('deleted', 0)
                    ->whereDate('date_attendance', '>=', $startDate)
                    ->whereDate('date_attendance', '<=', $endDate);
            }])
            ->with('tax')
            ->find($id);
    }
    public function getUser($userId, $timeFilters)
    {
        switch ($timeFilters['time_frame']) {
            case 'monthly':
                $startDate = $timeFilters['current_time']->copy()->startOfMonth()->startOfDay();
                $endDate = $timeFilters['current_time']->copy()->endOfMonth()->endOfDay();
                $type = 2;
                break;
            case '26,25':
            default:
                $startDate = $timeFilters['current_time']->copy()->subMonth()->startOfMonth()->addDays(25)->startOfDay();
                $endDate = $timeFilters['current_time']->copy()->startOfMonth()->addDays(24)->endOfDay();
                $type = 1;
                break;
        }

        $startYear = $timeFilters['current_time']->copy()->startOfYear();
        $endYear = $timeFilters['current_time']->copy()->endOfYear();
        return $this->with(['latest_salaries' => function ($q) use ($timeFilters, $startDate, $endDate, $type) {
            $q->with('group')->whereNull('deleted_at')
                ->where('valid_from', '<=', $endDate)
                ->where('type', $type)
                ->orderBy('valid_from', 'desc')
                ->orderBy('id', 'desc')
                ->groupBy('group_id');
        }])
            ->with(['salaries' => function ($q) use ($timeFilters, $startDate, $endDate, $type) {
                $q->with('group')->whereNull('deleted_at')
                    ->where('type', $type)
                    ->orderBy('level')
                    ->orderBy('valid_from')
                    ->orderBy('id', 'desc');
            }])
            ->with(['work_days' => function ($q) use ($timeFilters, $startDate, $endDate) {
                $q->with('group')
                    ->where('status', 0)
                    ->where('deleted', 0)
                    ->whereDate('date_attendance', '>=', $startDate)
                    ->whereDate('date_attendance', '<=', $endDate);
            }])
            ->with('tax')
            ->findById($userId);
    }

    public function onlineTeacher()
    {
        return $this->model->teacher()->with(['groups' => function ($q) {
            $q->whereNotNull('group_id')->with('group');
        }])->get();
    }

    public function getAllStudent()
    {
        return $this->model->student()->with('course_time_student')->get();
    }

    public function getOnlineTeachers()
    {
        return $this->model->teacher()->get();
    }
}
