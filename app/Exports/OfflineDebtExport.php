<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class OfflineDebtExport implements FromView, ShouldAutoSize
{
    protected $invoices;

    public function __construct(array $invoices)
    {
      $this->invoices = $invoices;
    }

    public function view(): View
    {
      return view('backend.offline.export-debt', [
        'invoices' => $this->invoices
      ]);
    }
}
