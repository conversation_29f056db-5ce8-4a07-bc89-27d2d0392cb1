"use strict";var user=new Vue({el:"#fill_active_code",data:{url:window.location.origin,errors:[],loading:!1},methods:{changeCaptcha:function(){var r="".concat(this.url,"/account/refresh-captcha?").concat(Date.now());$("#captcha-change").attr("src",r)},activeCourse:function(){var r=this,c=$("#pin").val(),o=$("#captcha").val(),n=$("#csrf_token").val();if(void 0==c||null==c||""==c||void 0==o||null==o||""==o)return r.errors=[],void r.errors.push("Thông tin còn trống");if(checkspecialSymbol(c)||checkspecialSymbol(o))return r.errors=[],void r.errors.push("Thông tin không được chứa ký tự đặc biệt");if(!r.loading){r.loading=!0;var e={_token:n,activeCode:c,captcha:o};$.ajax({url:window.location.origin+"/account/active-course",type:"POST",data:e,async:!0,beforeSend:function(r){if(n)return r.setRequestHeader("X-CSRF-TOKEN",n)},error:function(){return r.errors=[],r.errors.push("Có lỗi xảy ra. Vui lòng kiểm tra lại mã kích hoạt hoặc đổi mã xác thực. Lưu ý: thông tin không được chứa ký tự đặc biệt"),r.changeCaptcha(),r.loading=!1,!1},success:function(c){return r.loading=!1,"invalid_code"==c?(r.errors=[],r.errors.push("Mã kích hoạt không đúng"),void r.changeCaptcha()):"voucher_used"==c?(r.errors=[],r.errors.push("Mã kích hoạt đã sử dụng. Vui lòng sử dụng mã khác"),void r.changeCaptcha()):"voucher_expired"==c?(r.errors=[],r.errors.push("Mã cần xác thực, vui lòng liên hệ fanpage Dungmori để được kích hoạt"),void r.changeCaptcha()):(r.errors=[],void $("#successModal").modal("toggle"))}})}},gotoCourseUrl:function(){window.location.href=window.location.origin+"/account/courses"}},mounted:function(){$("#error-list").css("display","block")}});