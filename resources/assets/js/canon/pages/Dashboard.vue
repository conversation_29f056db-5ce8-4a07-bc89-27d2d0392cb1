<template>
  <div>
    <div>
      <!--      <div class="statistic flex items-center mb-3" v-loading="statisticLoading">-->
      <!--        <div class="p-3 mr-3  border">Số học viên: {{ totalResult }}</div>-->
      <!--        <div class="p-3 mr-3  border">% học trung bình: {{ averageProgress }}%</div>-->
      <!--        <div class="p-3 mr-3  border">Số học viên >= %TB: {{ aboveProgress }}</div>-->
      <!--        <div class="p-3 mr-3  border">Số học viên < %TB: {{ belowProgress }}</div>-->
      <!--        <el-button type="primary" @click="getStatistic">Lấy số liệu</el-button>-->
      <!--      </div>-->
      <div class="filter mb-3 flex items-center">
        <span class="h1 mr-3">K<PERSON><PERSON></span>
        <el-select
          v-model="filters.courseId"
          @change="changeCourse"
          placeholder="Select"
          class="mr-3"
          style="width: 100px"
        >
          <el-option label="N1" :value="17"></el-option>
          <el-option label="N2" :value="16"></el-option>
          <el-option label="N3" :value="3"></el-option>
          <el-option label="N4" :value="4"></el-option>
          <el-option label="N5" :value="5"></el-option>
          <el-option label="SC N5" :value="39"></el-option>
          <el-option label="SC N4" :value="40"></el-option>
        </el-select>
        <el-input
          class="mr-3"
          placeholder="Email"
          prefix-icon="el-icon-search"
          v-model="filters.email"
          @keyup.enter.native="getList"
          style="max-width: 300px"
        >
        </el-input>
        <el-select
          v-if="groups.length"
          v-model="filters.groupId"
          placeholder="Chọn nhóm lớp"
          class="mr-3"
          style="width: 100px"
        >
          <el-option
            v-for="group in groups"
            :key="group.id"
            :label="group.name"
            :value="group.id"
          ></el-option>
        </el-select>
        <el-select
          v-model="periodId"
          placeholder="Chọn một lộ trình"
          class="mr-3"
          style="width: 100px"
        >
          <el-option
            v-for="period in learningPeriods"
            :key="`period-${period.id}`"
            :label="period.description"
            :value="period.id"
          ></el-option>
        </el-select>
        <el-button type="primary" @click="getList">Tìm kiếm</el-button>
        <el-button type="primary" @click="handleExport(false, 'week')" :loading="exportLoading"
          >Xuất Excel Tuần</el-button
        >
        <el-button v-if="[39,40].includes(filters.courseId)" type="primary" @click="handleExport(false, 'day')" :loading="exportLoading"
        >Xuất Excel Ngày</el-button
        >
        <el-button
          v-if="![39,40].includes(filters.courseId)"
          type="primary"
          @click="handleExport(false)"
          :loading="exportLoading"
          >Xuất Excel %</el-button
        >
        <div class="ml-3">
          Tìm thấy <strong>{{ totalResult }}</strong> kết quả
        </div>
      </div>
      <!--      <el-slider-->
      <!--        v-model="progressRange"-->
      <!--        range-->
      <!--        show-stops-->
      <!--        :marks="marks"-->
      <!--        :max="100"-->
      <!--        style="max-width: 50%"-->
      <!--      >-->
      <!--      </el-slider>-->
    </div>
    <div class="mt-5">
      <i
        class="el-icon-document-copy cursor-pointer hover:text-blue-500"
        @click="copyText"
      ></i>
    </div>
    <el-table
      v-loading="loading"
      id="table"
      :data="tableData"
      border
      style="width: 100%; margin-top: 10px"
    >
      <el-table-column type="index" label="ID" width="70"> </el-table-column>
      <el-table-column prop="name" label="Họ tên" width="200">
        <template slot-scope="scope">
          <div>
            <div class="flex">
              {{ scope.row.name }}
              <a :href="loginUser(scope.row)" target="_blank"
                ><i
                  class="fa fa-user-circle text-md"
                  style="font-size: 16px; color: #96d962; margin-left: 2px"
                ></i
              ></a>
            </div>
            <div class="flex gap-1 items-center flex-wrap">
              <span
                v-if="!scope.row.fingerprint && !scope.row.platform"
                class="inline-flex items-center rounded-md bg-red-100 px-2 py-0 text-xs font-medium text-red-700"
              >
                Chưa đăng nhập</span
              >
              <span
                v-else-if="
                  !scope.row.progress.length
                  // !scope.row.lesson_tracking_stat.length
                "
                class="inline-flex items-center rounded-md bg-yellow-100 px-1.5 py-0 text-xs font-medium text-yellow-800"
              >
                Chưa học</span
              >
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="email" label="Email" width="180">
      </el-table-column>
      <el-table-column prop="last_lesson" label="Bài cuối" width="180">
        <template slot-scope="scope">
          <span>Buổi {{ scope.row.last_lesson ? scope.row.last_lesson?.checkpoint?.key : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="totalPercent" label="Tổng">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="`${scope.row.total / 100} / ${courseLessonCount}`" placement="top-start">
            <span>{{ scope.row.totalPercent }}</span>
          </el-tooltip>
          <div>
            {{
              `${
                Math.floor(moment.duration(scope.row.totalHour).asHours()) || ""
              }${
                Math.floor(moment.duration(scope.row.totalHour).asHours())
                  ? "h"
                  : ""
              }${moment.duration(scope.row.totalHour).minutes()}${
                moment.duration(scope.row.totalHour).minutes() ? "p" : ""
              }`
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-for="category in categories"
        :key="`column-category-${category.id}`"
        :label="category.title"
      >
        <template slot-scope="scope">
          <div>{{ scope.row[`category-${category.id}`]?.percentage }}</div>
          <div>{{ scope.row[`category-${category.id}`]?.timer }}</div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="Chọn lộ trình" :visible.sync="exportDialog">
      <span slot="footer" class="dialog-footer">
        <el-button
          @click="
            exportDialog = false;
            periodId = '';
          "
          >Đóng</el-button
        >
        <el-button
          type="primary"
          @click="exportExcel(exportAll)"
          :loading="exportLoading"
          >Xuất file</el-button
        >
      </span>
    </el-dialog>
    <div style="margin-top: 10px">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="page"
        :page-sizes="[100, 20, 50, 100]"
        :page-size.sync="perPage"
        layout="prev, pager, next"
        :total="totalResult"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import { find, sumBy, groupBy, map } from "lodash";
import axios from "axios";
import moment from "moment";

export default {
  data() {
    return {
      results: [],
      page: 1,
      perPage: 100,
      loading: false,
      exportLoading: false,
      statisticLoading: false,
      progressRange: [0, 100],
      vendor: "canon",
      moment: moment,
      filters: {
        courseId: 17,
        email: "",
        groupId: "",
      },
      periodId: "",
      learningPeriods: [],
      exportDialog: false,
      exportAll: false,
      marks: {
        0: "0%",
        10: "10%",
        20: "20%",
        30: "30%",
        40: "40%",
        50: "50%",
        60: "60%",
        70: "70%",
        80: "80%",
        90: "90%",
        100: "100%",
      },
    };
  },
  computed: {
    ...mapGetters("ui", [
      "me",
      "users",
      "categories",
      "courseLessonCount",
      "totalResult",
      "averageProgress",
      "aboveProgress",
      "belowProgress",
      "groups",
      "exerciseComponents",
      "lessons",
    ]),
    tableData() {
      return this.users.map((user) => {
        user.total = 0;
        user.last_lesson = user.lesson_progress.length
          ? user.lesson_progress[0]
          : null;
        user.categories = this.categories.map((item) => {
          item.lessons = [];
          item.total = 0;
          item.groups.forEach((group) => {
            group.lessons_stat.forEach((lesson) => {
              if (group.show && lesson.show === 1) {
                item.lessons.push(lesson);
                const foundItem = find(user.lesson_progress, { lesson_id: lesson.id });
                if (foundItem) {
                  const components = lesson.component.map(c => c.type)
                  const set = new Set(components);
                  const isExercise = this.exerciseComponents.some(item => set.has(item));

                  const exampleProgress = parseInt(foundItem.example_progress || 0);
                  const videoProgress = parseInt(foundItem.video_progress || 0);
                  item.total += isExercise ? (exampleProgress >= 85 ? 100 : 0) : (videoProgress >= 85 ? 100 : 0);
                  user.total += isExercise ? (exampleProgress >= 85 ? 100 : 0) : (videoProgress >= 85 ? 100 : 0);
                }
              }
            })
          });

          item.average = item.lessons.length ? item.total / item.lessons.length : 0;
          const matchStat = user.lesson_tracking_stat.filter(
            (o) => parseInt(o.category_id) === parseInt(item.id)
          );
          const timer = sumBy(matchStat, (o) => parseInt(o.spent_time));

          user[`category-${item.id}`] = {
            percentage: `${item.average.toFixed(2)}%`,
            timer: `${moment.duration(timer).hours() || ""}${
              moment.duration(timer).hours() ? "h" : ""
            }
            ${moment.duration(timer).minutes() || ""}${
              moment.duration(timer).minutes() ? "m" : ""
            }
            ${moment.duration(timer).seconds() || ""}${
              moment.duration(timer).seconds() ? "s" : ""
            }
            `,
          };
          return { ...item, total: item.average, timer: timer };
        });
        user.totalPercent = `${(user.total / this.courseLessonCount).toFixed(
          2
        )}%`;
        user.totalHour = sumBy(user.categories, "timer");
        return user;
      });
    },
  },
  methods: {
    ...mapActions("ui", ["getUserList", "getUserStatistic", "getMe"]),
    getLesson(lessonId) {
      const lesson = lessonId ? this.lessons.find((lesson) => lesson.id === lessonId) : null;
      return lesson ? lesson.name : "";
    },
    async getList() {
      this.loading = true;
      const data = {
        ...this.filters,
        progressFrom: this.progressRange[0],
        progressTo: this.progressRange[1],
        vendor: this.vendor,
        page: this.page,
        perPage: this.perPage,
        periodId: this.periodId,
      };
      await this.getUserList(data);
      this.loading = false;
    },
    async getStatistic() {
      this.statisticLoading = true;
      const data = {
        ...this.filters,
        progressFrom: this.progressRange[0],
        progressTo: this.progressRange[1],
        vendor: this.vendor,
        page: this.page,
        perPage: this.perPage,
      };
      await this.getUserStatistic(data);
      this.statisticLoading = false;
    },
    async handleCurrentChange(page) {
      await this.getList();
    },
    async handleSizeChange(perPage) {
      await this.getList();
    },
    async changeCourse(courseId) {
      await this.getLearningPeriods();
    },
    async getLearningPeriods() {
      let vm = this;
      const params = {
        courseId: this.filters.courseId,
      };
      axios
        .get(window.location.origin + "/business/api/v1/get-learning-periods", {
          params,
        })
        .then(async (res) => {
          vm.learningPeriods = res.data.data;
          vm.periodId = res.data.data[0].id;
          await vm.getList();
        });
    },
    handleExport(exportAll, type) {
      this.exportExcel(exportAll, type);
    },
    async exportExcel(exportAll = true, type = 'week') {
      let vm = this;
      if (vm.exportLoading) return;
      vm.exportLoading = true;
      const data = {
        ...this.filters,
        progressFrom: this.progressRange[0],
        progressTo: this.progressRange[1],
        vendor: this.vendor,
        page: this.page,
        perPage: this.perPage,
        exportAll: exportAll ? 1 : 0,
        periodId: this.periodId,
        type: type,
      };
      axios
        .post(window.location.origin + "/business/api/v1/export", data, {
          responseType: "blob",
        })
        .then((response) => {
          const url = URL.createObjectURL(
            new Blob([response.data], {
              type: "application/vnd.ms-excel",
            })
          );
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute(
            "download",
            "tien_trinh_hoc_" + (type === 'week' ? 'theo_tuan_' : 'theo_ngay_') + Date.now() + ".xlsx"
          );
          document.body.appendChild(link);
          link.click();
          vm.exportDialog = false;
          vm.exportLoading = false;
        })
        .catch((res) => {
          vm.exportLoading = false;
        });
    },
    loginUser(row) {
      return window.location.origin + "/business/login-user/" + row.id;
    },
    copyText() {
      var el = document.getElementById("table");

      var body = document.body,
        range,
        sel;

      if (document.createRange && window.getSelection) {
        range = document.createRange();

        sel = window.getSelection();

        sel.removeAllRanges();

        try {
          range.selectNodeContents(el);

          sel.addRange(range);
        } catch (e) {
          range.selectNode(el);

          sel.addRange(range);
        }
      } else if (body.createTextRange) {
        range = body.createTextRange();

        range.moveToElementText(el);

        range.select();
      }

      document.execCommand("Copy");
    },
  },
  async mounted() {
    await this.getLearningPeriods();
    await this.getMe();
  },
};
</script>
<style>
.el-table .cell {
  word-break: break-word;
}
</style>
