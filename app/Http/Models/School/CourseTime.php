<?php

namespace App\Http\Models\School;

use App\Base\Model\BaseModel;
use App\Http\Models\Community\CommunityGroup;
use Illuminate\Support\Facades\DB;

class CourseTime extends BaseModel
{
    protected $connection = 'mysql3';
    protected $table = 'course_times_tbl';
    protected $table_name = 'course_times_tbl';

    protected $fillable = ['group_id', 'teacher_id', 'index', 'date_attendance', 'create_person', 'reward', 'ref_reward', 'link', 'fault', 'created', 'updated', 'note', 'note_cancel', 'password'];
    const CREATED_AT = 'created';
    const UPDATED_AT = 'updated';

    public function __construct() {
      $this->table = DB::connection($this->connection)->getDatabaseName() . '.' . $this->table_name;
    }

    public function course()
    {
      return $this->belongsTo(Course::class, 'course_id');
    }

    public function group()
    {
      return $this->belongsTo(CommunityGroup::class, 'group_id');
    }

    public function present()
    {
      return $this->hasMany(CourseTimeStudent::class, 'course_time_id')->where('deleted', 0)->where('status', 1);
    }

    public function absence()
    {
        return $this->hasMany(CourseTimeStudent::class, 'course_time_id')->where('deleted', 0)->where('status', 0);
    }

    public function noReasonAbsence()
    {
        return $this->hasMany(CourseTimeStudent::class, 'course_time_id')->where('deleted', 0)->where('status', 0)->where(function ($q) {
            $q->whereNull('note')->orWhere('note', '');
        });
    }

    public function attendances()
    {
      return $this->hasMany(CourseTimeStudent::class, 'course_time_id')->where('deleted', 0);
    }
}
