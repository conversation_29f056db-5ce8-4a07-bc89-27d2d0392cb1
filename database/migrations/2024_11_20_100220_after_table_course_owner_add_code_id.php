<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('course_owner', function (Blueprint $table) {
            $table->unsignedInteger('code_id')->default(null)->nullable()->after('extra_expired_day');
            $table->foreign('code_id')->references('id')->on('codes')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('course_owner', function (Blueprint $table) {
            $table->dropColumn('code_id');
        });
    }
};
