<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class LessonComment extends Model{

    protected $table = 'lesson';

    protected $appends = ['course_url'];

    public function getCourseUrlAttribute(){

        return $this->getCourse['SEOurl'];

    }
    //lấy ra course cha
    public function getCourse(){

    	return $this->hasOne('App\Http\Models\LessonCourse', 'id', 'course_id');
    }

}
