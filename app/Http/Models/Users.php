<?php

namespace App\Http\Models;

use App\Http\Models\Community\CommunityGroup;
use App\Http\Models\Community\CommunityGroupUser;
use App\Http\Models\School\CourseTime;
use App\Http\Models\School\CourseTimeStudent;
use App\Http\Models\School\SchoolUser;
use App\Http\Models\ThiThu\Result;
use App\Http\ModelsFrontend\LessonProgress;
use App\Http\Traits\Filterable;
use App\QueryFilter;
use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;
use Laravel\Scout\Searchable;
use MongoDB\Laravel\Eloquent\HybridRelations;

class Users extends Model
{
    use HybridRelations;
    use Searchable;
    use Filterable;

    protected $connection = 'mysql';
    protected $table = 'users';

    public $timestamps = false;

    protected $fillable = ['email', 'name', 'email', 'password', 'year', 'gender', 'is_tester'];

    protected $hidden = [
        'remember_token', 'password', 'pin', 'birthday_year', 'action', 'action_time', 'activation',
        'balance', 'balance_decode', 'blocked', 'last_login', 'user_group_id', 'last_ip', 'verify_token'
    ];

    /**
     * Retrieve the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
      return 'users_index';
    }
    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
      return $date->format('Y-m-d H:i:s');
    }

    function getIndexName()
    {
      return 'users';
    }

    public function conversation() {
        return $this->hasOne(Conversation::class, 'creator_id')->whereNull('friend_id')->whereNull('group_id')->select('id', 'creator_id');
    }

    public function groups() {
        return $this->belongsToMany(CommunityGroup::class, 'community_group_users', 'user_id', 'group_id');
    }

    public function course_owner()
    {
        return $this->hasMany(CourseOwner::class, 'owner_id', 'id');
    }

    public function progress() {
        return $this->hasMany(CourseProgress::class, 'user_id', 'id');
    }

    public function lesson_progress() {
        return $this->hasMany(LessonProgress::class, 'user_id', 'id');
    }

    public function course_time() {
        return $this->hasMany(CourseTimeStudent::class, 'student_id');
    }

    public function group_user() {
      return $this->hasMany(CommunityGroupUser::class, 'user_id');
    }

    public function offline_user()
    {
      return $this->hasOne(SchoolUser::class, 'dmr_id');
    }

    public function lesson_tracking_stat()
    {
      return $this->hasMany(LessonTrackingStat::class, 'user_id');
    }

    public function histories()
    {
      return $this->hasMany(LessonHistory::class, 'user_id');
    }

    public function results()
    {
      return $this->hasMany(Result::class, 'user_id');
    }

    public function lesson_results()
    {
        return $this->hasMany(LessonResult::class, 'user_id')->orderByDesc('total_grade');
    }

    public function exam_results()
    {
        return $this->hasMany(ExamResult::class, 'user_id')->orderByDesc('total_score');
    }

    public function latestCourseTimeStudent()
    {
      return $this->hasOne(CourseTimeStudent::class, 'student_id')->where('deleted',0)->latestOfMany();
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
      return [
        'name' => $this->name,
        'email' => $this->email
      ];
    }

}
