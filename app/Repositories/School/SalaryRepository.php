<?php
namespace App\Repositories\School;

use App\Base\Repository\EloquentRepository;
use App\Http\Models\School\Salary;

class SalaryRepository extends EloquentRepository
{
  /**
   * @param Salary $model
   */
  public function __construct(Salary $model)
  {
    parent::__construct($model);
  }

  /**
   * @param $where
   * @return mixed
   */
  public function getSalariesByUser($where, $teacherType)
  {
      if ($teacherType == 'online') {
          return $this->model->whereNotNull('group_id')->where($where)->orderBy('valid_from', 'desc')->orderBy('id', 'desc')->get();
      } else {
          return $this->model->whereNotNull('course_id')->where($where)->orderBy('valid_from', 'desc')->orderBy('id', 'desc')->get();
      }
  }
}
