<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('combo', function (Blueprint $table) {
            $table->string('name_desc', 255)->nullable()->after('name');
            $table->string('note', 255)->nullable()->after('image_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('combo', function (Blueprint $table) {
            $table->dropColumn('name_desc');
            $table->dropColumn('note');
        });
    }
};
