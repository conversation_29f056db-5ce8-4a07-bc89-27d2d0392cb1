{"version": 3, "sources": ["new_dashboard.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "new_dashboard.js", "sourcesContent": ["var user = new Vue({\n    el: '#new-dashboard',\n\n    data: function () {\n        return {\n            url: window.location.origin,\n            totalGender: [],\n            genderByCourse: []\n        };\n    },\n    mounted: function () {\n        vm = this;\n        vm.getAllGender();\n        vm.getGenderByCourse();\n    },\n    methods: {\n        getAllGender: function() {\n            vm = this;\n            $.get(vm.url + '/backend/new-dashboard/total-gender', function (res) {\n                vm.totalGender = res;\n            });\n        },\n        getGenderByCourse: function () {\n            vm = this;\n            $.get(vm.url + '/backend/new-dashboard/gender-by-course', function (res) {\n                var data = _.map(res.data, function (value, key) {\n                    value = _.map(value, function (value, key) {\n                        var gender = {\n                            name: key ? key : 'Khác',\n                            y: value\n                        }\n                        return gender;\n                    });\n                    var course = {\n                        name: key,\n                        genders: _.orderBy(value, 'name', 'desc')\n                    };\n                    return course;\n                });\n                vm.genderByCourse = _.orderBy(data, 'name', 'asc');\n            });\n        },\n    }\n});"]}