<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE `lesson` MODIFY COLUMN `type` ENUM('video','video_test','test','docs','flashcard','checkpoint','guide','exam','last_exam') NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE `lesson` MODIFY COLUMN `type` ENUM('video','video_test','test','docs','flashcard','checkpoint','guide','short_test','long_test') NOT NULL");
    }
};
