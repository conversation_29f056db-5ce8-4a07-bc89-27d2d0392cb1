/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'table', 'he', {
	border: 'גודל מסגרת',
	caption: 'כיתוב',
	cell: {
		menu: 'מאפייני תא',
		insertBefore: 'הוספת תא לפני',
		insertAfter: 'הוספת תא אחרי',
		deleteCell: 'מחיקת תאים',
		merge: 'מיזוג תאים',
		mergeRight: 'מזג ימינה',
		mergeDown: 'מזג למטה',
		splitHorizontal: 'פיצול תא אופקית',
		splitVertical: 'פיצול תא אנכית',
		title: 'תכונות התא',
		cellType: 'סוג התא',
		rowSpan: 'מתיחת השורות',
		colSpan: 'מתיחת התאים',
		wordWrap: 'מניעת גלישת שורות',
		hAlign: 'יישור אופקי',
		vAlign: 'יישור אנכי',
		alignBaseline: 'שורת בסיס',
		bgColor: 'צבע רקע',
		borderColor: 'צבע מסגרת',
		data: 'מידע',
		header: 'כותרת',
		yes: 'כן',
		no: 'לא',
		invalidWidth: 'שדה רוחב התא חייב להיות מספר.',
		invalidHeight: 'שדה גובה התא חייב להיות מספר.',
		invalidRowSpan: 'שדה מתיחת השורות חייב להיות מספר שלם.',
		invalidColSpan: 'שדה מתיחת העמודות חייב להיות מספר שלם.',
		chooseColor: 'בחר'
	},
	cellPad: 'ריפוד תא',
	cellSpace: 'מרווח תא',
	column: {
		menu: 'עמודה',
		insertBefore: 'הוספת עמודה לפני',
		insertAfter: 'הוספת עמודה אחרי',
		deleteColumn: 'מחיקת עמודות'
	},
	columns: 'עמודות',
	deleteTable: 'מחק טבלה',
	headers: 'כותרות',
	headersBoth: 'שניהם',
	headersColumn: 'עמודה ראשונה',
	headersNone: 'אין',
	headersRow: 'שורה ראשונה',
	heightUnit: 'height unit', // MISSING
	invalidBorder: 'שדה גודל המסגרת חייב להיות מספר.',
	invalidCellPadding: 'שדה ריפוד התאים חייב להיות מספר חיובי.',
	invalidCellSpacing: 'שדה ריווח התאים חייב להיות מספר חיובי.',
	invalidCols: 'שדה מספר העמודות חייב להיות מספר גדול מ 0.',
	invalidHeight: 'שדה גובה הטבלה חייב להיות מספר.',
	invalidRows: 'שדה מספר השורות חייב להיות מספר גדול מ 0.',
	invalidWidth: 'שדה רוחב הטבלה חייב להיות מספר.',
	menu: 'מאפייני טבלה',
	row: {
		menu: 'שורה',
		insertBefore: 'הוספת שורה לפני',
		insertAfter: 'הוספת שורה אחרי',
		deleteRow: 'מחיקת שורות'
	},
	rows: 'שורות',
	summary: 'תקציר',
	title: 'מאפייני טבלה',
	toolbar: 'טבלה',
	widthPc: 'אחוז',
	widthPx: 'פיקסלים',
	widthUnit: 'יחידת רוחב'
} );
