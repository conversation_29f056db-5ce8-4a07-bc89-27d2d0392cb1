<template>
  <div class="mt-5 font-averta-regular h-[calc(100vh-245px)] overflow-y-auto">
    <div class="text-xl text-[#073A3B] font-averta-semibold">
      {{ stageName }}
    </div>
    <div>
      <div
        v-for="category in menuData"
        :key="`category-${category.id}`"
        class="lesson-group-item my-2"
        :class="
          openingCategoryIds.includes(+category.id) ? 'is-open' : ''
        "
      >
        <div
          class="flex items-center cursor-pointer lesson-group-item__head relative"
          @click="toogleOpenCategory(+category.id)"
        >
          <div
            class="flex-none max-w-[90%] font-averta-semibold text-xl text-[#073A3B]"
          >
            {{ category.title }}
          </div>
          <div class="ml-4 w-full h-[2px] bg-[#D0D3DA] mr-10"></div>
          <div class="absolute right-4">
            <img
              class="arrow-icon w-2"
              src="/images/icons/arrow-right.png"
              alt="arrow"
            />
          </div>
        </div>
        <div>
          <div v-for="group in category.groups" :key="`group-${group.id}`">
            <div
              class="py-2 grid grid-cols-1 gap-4 group-list relative font-averta-regular"
              :class="openingGroupIds.includes(group.id) ? 'is-open' : ''"
            >
              <div
                class="flex items-center relative"
                @click="toogleOpenGroup(group.id)"
              >
                <div class="flex-none">
                  <img
                    class="w-[16px]"
                    src="/images/icons/play2.png"
                    alt="play button"
                  />
                </div>
                <div
                  class="ml-4"
                  :class="!isUnlock ? 'text-[#757575]' : 'text-[#1E1E1E]'"
                >
                  <div class="mb-1">
                    <span
                      class="text-xs text-white bg-[#57D061] py-1 px-2 rounded-full"
                      v-if="group.is_trial"
                    >
                      Học thử
                    </span>
                  </div>
                  <div class="text-xl cursor-pointer">
                    <span class="font-averta-semibold">{{ group.name }}</span>
                  </div>
                  <div :class="colorGroup(group)">
                    {{ group.total_video }} videos bài giảng・{{
                      group.total_time[0] > 0
                        ? group.total_time[0] + " tiếng "
                        : ""
                    }}
                    {{ group.total_time[1] }} phút
                  </div>
                </div>
                <div
                  class="absolute right-4"
                  :class="
                    !isUnlock && !group.is_trial ? 'show-lock' : 'show-arrow'
                  "
                >
                  <svg
                    v-if="
                      (group.lessons.filter(l => l.require).length === 0 &&
                      group.lessons.filter(l => l.progress >= 85).length === group.lessons.length) ||
                      (group.lessons.filter(l => l.require).length > 0 &&
                      group.lessons.filter(l => l.progress >= 85 && l.require).length === group.lessons.filter(l => l.require).length)
                    "
                    width="14"
                    height="10"
                    viewBox="0 0 14 10"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M12.4191 1L5.08577 8.33333L1.75244 5" stroke="#57D061"
                          stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round"/>
                  </svg>
                  <img
                    v-else
                    class="w-2 arrow-group"
                    src="/images/icons/arrow-right.png"
                    alt="arrow"
                  />
                  <span class="lock-icon">
                    <img
                      class="w-[20px]"
                      src="/images/icons/lock.png"
                      alt="lock.png"
                    />
                  </span>
                </div>
              </div>
              <div class="lessons pl-5 ml-3 border-l-2 border-[#d0d3da]">
                <div
                  v-for="lesson in group.lessons"
                  :key="`lesson-${lesson.id}`"
                  class="lesson my-2 py-2 cursor-pointer bg-cover bg-center bg-no-repeat"
                  :id="lesson.id === currentLesson.id ? 'current-lesson' : ''"
                  :style="lesson.id === currentLesson.id ? Number(lesson.progress) >= 85 ? 'background: linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%);' : 'background: linear-gradient(90deg, #FFFFFF 0%, #FFFBEB 31%, #FFFBEB 70%, #FFFFFF 100%)' : ''"
                  @click="goToLesson(lesson)"
                >
                  <div class="flex items-center">
                    <div
                      :class="`flex-none rounded-[24px] relative overflow-hidden w-[115px] h-[65px] bg-cover bg-center`"
                      :style="`background-image: url('${lesson.image}')`"
                    >
                      <img
                        v-if="lesson.component_types.includes(2)"
                        class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[20px]"
                        src="/images/icons/play-button.png"
                        alt="play-button.png"
                      />
                    </div>
                    <div class="ml-3 w-full">
                      <div class="relative truncate flex" :class="lessonNameClass(lesson)">
                        <div
                          v-if="lesson.name_html"
                          class="flex text-base truncate max-w-[170px] sp:max-w-[140px]"
                        >
                          <div class="name-lesson w-full flex text-base truncate" v-html="lesson.name_html"></div>
                          <img
                              v-if="lesson.require"
                              class="w-[12px] h-[12px]  min-w-[12px]"
                              :src="`/images/icons/${
                                isPassedLesson(lesson) ? 'locked/' : ''
                              }require.png`"
                              alt="require.png"
                          />
                        </div>
                        <div
                          v-else
                          class="text-base truncate max-w-[170px] sp:max-w-[140px]"
                        >
                          <div class="relative name-lesson truncate icon-require pr-[15px]"
                            >{{ lesson.name }}
                            <img
                              v-if="lesson.require"
                              class="w-[12px] min-w-[12px] absolute right-0"
                              :src="`/images/icons/${
                                isPassedLesson(lesson) ? 'locked/' : ''
                              }require.png`"
                              alt="require.png"
                            />
                          </div>
                        </div>
                      </div>
                      <template v-if="['last_exam'].includes(lesson.type)">
                        <div v-if="lesson.passed_exam_result" class="mt-1 font-averta-semibold text-[14px] text-[#07403F] bg-[#86F082] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center">Đạt</div>
                        <div v-else-if="lesson.last_exam_result && !lesson.last_exam_result.submit_at" class="mt-1 font-averta-semibold text-[14px] text-[#414348] bg-[#F5F5F5] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center">Chưa nộp</div>
                        <div v-else-if="lesson.exam_result && !lesson.passed_exam_result" class="mt-1 font-averta-semibold text-[14px] text-[#682D03] bg-[#FFF1C2] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center">Chưa đạt</div>
                      </template>
                      <div
                        class="flex flex-wrap gap-y-1 gap-x-2 mt-2"
                        :class="lessonNameClass(lesson)"
                      >
                        <div
                          v-if="lesson.component_types.includes(2)"
                          class="flex items-center"
                        >
                          <img
                            class="w-[12px] flex-none"
                            :src="`/images/icons/${
                              isLocked(lesson) ? 'locked/' : ''
                            }play2.png`"
                            alt="play2.png"
                          />
                          <span class="ml-1 text-sm"> Video </span>
                        </div>
                        <div
                          v-if="
                            lesson.component_types.includes(3) ||
                            lesson.component_types.includes(13) ||
                            lesson.component_types.includes(14) ||
                            lesson.component_types.includes(15) ||
                            lesson.component_types.includes(16) ||
                            lesson.component_types.includes(17)
                          "
                          class="flex items-center"
                        >
                          <img v-if="lesson.lesson_type === 'flashcard'"
                              class="w-[12px] flex-none"
                              src="/images/icons/tag-flashcard.svg"
                              alt="file.png"
                          />
                          <img v-else
                            class="w-[12px] flex-none"
                            :src="`/images/icons/${
                              isLocked(lesson) ? 'locked/' : ''
                            }file.png`"
                            alt="file.png"
                          />
                          <span class="ml-1 text-sm"> {{ lesson.lesson_type === 'flashcard' ? lesson.component_types.filter(i => i === 17).length + ' Từ vựng' : 'Bài tập' }} </span>
                        </div>

                        <div
                          v-if="
                            lesson.component_types.includes(1) ||
                            lesson.component_types.includes(8)
                          "
                          class="flex items-center"
                        >
                          <img
                            class="w-[12px] flex-none"
                            :src="`/images/icons/${
                              isLocked(lesson) ? 'locked/' : ''
                            }docs.png`"
                            alt="docs.png"
                          />
                          <span class="ml-1 text-sm"> Tài liệu </span>
                        </div>

                        <div
                          v-if="lesson.component_types.includes(5)"
                          class="flex items-center"
                        >
                          <img
                            class="w-[12px] flex-none"
                            :src="`/images/icons/${
                              isLocked(lesson) ? 'locked/' : ''
                            }speaker.png`"
                            alt="speaker.png"
                          />
                          <span class="ml-1 text-sm"> File âm thanh </span>
                        </div>

                        <div
                          v-if="lesson.expect_time"
                          class="flex items-center"
                        >
                          <img
                            class="w-[12px] flex-none"
                            :src="`/images/icons/${
                              isLocked(lesson) ? 'locked/' : ''
                            }clock.png`"
                            alt="clock.png"
                          />
                          <span class="ml-1 text-sm">
                            {{
                              convertMinutesToHourAndMinute(lesson.expect_time)
                            }}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div
                      v-if="!isUnlock && !lesson.is_free"
                      class="w-[36px] h-[36px] flex-none flex items-center justify-center ml-auto"
                    >
                      <img
                        class="w-[20px]"
                        src="/images/icons/lock.png"
                        alt="lock"
                      />
                    </div>
                    <template v-else-if="!['exam', 'last_exam'].includes(lesson.type) && lesson.progress < 85">
                      <Percentage :percent="lesson.progress" />
                    </template>
                    <div
                      v-else-if="
                      (['exam', 'last_exam'].includes(lesson.type) && lesson.passed_exam_result && lesson.progress >= 85)
                      || (!['exam', 'last_exam'].includes(lesson.type) && lesson.progress >= 85)"
                      class="w-[36px] h-[36px] flex-none flex items-center justify-center ml-auto"
                    >
                      <img
                        class="w-[24px]"
                        src="/images/icons/done.png"
                        alt="done.png"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Percentage from "./Percentage.vue";
export default {
  props: ["stageName", "isUnlock", "examStage"],
  components: {
    Percentage,
  },
  data() {
    return {
      // Data from lesson basic new blade
      menuData: menuData,
      currentLesson: currentLesson,
      openingGroupIds: [],
      openingCategoryIds: [],
      course: course,
      isUnlock: isUnlock,
      userLesson: userLesson
    };
  },
  mounted() {
    this.toogleOpenGroup(this.currentLesson.group_id);
    this.toogleOpenCategory(+this.currentLesson.category_id, true);
  },
  methods: {
    colorGroup(group) {
      return !this.isUnlock || group.progress >= 100
        ? "text-[#757575]"
        : "text-[#1E1E1E]";
    },
    isPassedLesson(lesson) {
      return lesson.progress >= 85;
    },
    lessonNameClass(lesson) {
      if (this.isPassedLesson(lesson)) {
        return "text-[#757575]";
      }
      if (lesson.is_free || this.isUnlock) {
        return "text-[#1E1E1E]";
      }
      return "text-[#757575]";
    },
    toogleOpenGroup(groupId) {
      groupId = Number(groupId);
      if (this.openingGroupIds.includes(groupId)) {
        this.openingGroupIds = this.openingGroupIds.filter(
          (id) => id !== groupId
        );
      } else {
        this.openingGroupIds.push(groupId);
        setTimeout(() => {
          fixRequireButton();
        }, 500);
      }
    },
    toogleOpenCategory(categoryId, is_mounted = false) {
      if (is_mounted) {
        this.openingCategoryIds = this.menuData.map(item => +item.id);
        this.openingCategoryIds = this.openingCategoryIds.filter(
            (id) => +id === +categoryId
        );
      } else  {
        this.openingCategoryIds.push(categoryId);
        this.openingCategoryIds = this.openingCategoryIds.filter(
            (id) => +id !== +categoryId
        );
      }
      console.log("Updated Opening IDs:", this.openingCategoryIds);
    },
    async goToLesson(lesson) {
      console.log(1111111)
      const that = this;
      const url = `/khoa-hoc/${this.course.SEOurl}/lesson/${lesson.id}-${lesson.slug}`;

      // if (!this.userLesson || (!this.isUnlock && (this.userLesson || this.currentLesson.price_option))) {
      //   window.location.href = url;
      //   return;
      // }

      let tabActive = localStorage.getItem('tab-active-lesson')
      let userResult = localStorage.getItem(`userResult-lesson-${this.currentLesson.id}`)
      console.log(`tabActive: `, tabActive)
      if (userResult) {
        userResult = JSON.parse(userResult);
      }
      console.log(`userResult: `, userResult)
      console.log(`currentLesson: `, this.currentLesson)
      if (this.currentLesson.type === 'last_exam') {
        window.location.href = url;
        return;
      }
      await $.get(
        window.location.origin + "/get-lesson-percent/" + this.currentLesson.id
      ).then((res) => {
        if (that.currentLesson.type === 'exam') {
          if (that.examStage > 0 && that.examStage < 4) {
            $("#modal-confirm-leave-exam")
              .find(".confirm-leave__title")
              .text("Bạn muốn chuyển bài?");

            $("#span_text_exercise").text("Cố gắng học tiếp để hoàn thành tiến trình bạn iu nha!");
            $("#modal-confirm-leave-exam").find(".btn-confirm-leave").text("Chuyển");
            $("#modal-confirm-leave-exam").modal("show");
            $("#modal-confirm-leave-exam").data("url", url);
            return;
          } else if (that.currentLesson.require && res < 85) {
            $("#modal-confirm-leave")
              .find(".confirm-leave__title")
              .text("Bạn muốn chuyển bài?");

            $("#span_text_exercise").text("Bài học có điểm số đạt <85% là bài học chưa đạt định mức hoàn thành");
            $("#modal-confirm-leave").find(".btn-confirm-leave").text("Chuyển");
            $("#modal-confirm-leave").modal("show");
            $("#modal-confirm-leave").data("url", url);
            return;
          } else {
            window.location.href = url;
          }
        } else if (tabActive === "tabExercise" ) {
          if (!that.currentLesson.require) {
            window.location.href = url;
          }

          if (that.currentLesson.require && userResult.ratio_correct_answer < 0.85 && res < 85) {
            $("#modal-confirm-leave")
              .find(".confirm-leave__title")
              .text("Bạn muốn chuyển bài?");

            $("#span_text_exercise").text("Bài học có điểm số đạt <85% là bài học chưa đạt định mức hoàn thành");
            $("#modal-confirm-leave").find(".btn-confirm-leave").text("Chuyển");
            $("#modal-confirm-leave").modal("show");
            $("#modal-confirm-leave").data("url", url);
            return;
          } else {
            window.location.href = url;
          }
        } else {
          if (that.currentLesson.require && res < 85) {
            $("#modal-confirm-leave").modal("show");
            $("#modal-confirm-leave")
              .find(".confirm-leave__title")
              .text("Bạn muốn chuyển bài?");
            $("#modal-confirm-leave").find(".btn-confirm-leave").text("Chuyển");
            $("#modal-confirm-leave").data("url", url);
          } else if (lesson.is_free || that.isUnlock) {
            window.location.href = url;
          }
        }
      });

    },
    convertMinutesToHourAndMinute(minutes) {
      const hours = Math.floor(minutes / 60);
      const remainMinutes = minutes % 60;
      return `${hours}h${remainMinutes}'`;
    },
    isLocked(lesson) {
      return !this.isUnlock && !lesson.is_free;
    },
  },
};
</script>
