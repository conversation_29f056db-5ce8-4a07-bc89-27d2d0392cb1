<?php

namespace App\Http\Models\Community;

use App\Http\Traits\Filterable;
use App\Base\Model\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommunityReportContent extends BaseModel
{
    use Filterable;
    use SoftDeletes;

    protected $table = 'community_report_contents';

    protected $fillable = [
        'name',
        'slug',
        'created_by',
        'is_public',
        'is_newfeed',
        'expired_at',
    ];
}
