<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as BaseVerifier;

class VerifyCsrfToken extends BaseVerifier
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        '/oauth/apple/callback',
        '/cdbh/api/reset-user-progress',
        '/cdbh/api/active-course',
        '/backend/ckfinder/*',
        '/api/api-sv/*',
        'http://dmr.cunhantructuyen.edu.vn/*',
        'https://dmr.cunhantructuyen.edu.vn/*'
    ];


    /**
     * The domains that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $exceptDomains = [
        'dmr.cunhantructuyen.edu.vn',
    ];
}
