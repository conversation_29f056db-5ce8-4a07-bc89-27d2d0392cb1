---
title: Autoplay Demo
subTitle: Autoplay
nav: demos
description: Autoplay usage demo
sort: 4

tags: 
- demo
- plugin
---

<div class="owl-carousel owl-theme">
	<div class="item"><h4>1</h4></div>
	<div class="item"><h4>2</h4></div>
	<div class="item"><h4>3</h4></div>
	<div class="item"><h4>4</h4></div>
	<div class="item"><h4>5</h4></div>
	<div class="item"><h4>6</h4></div>
	<div class="item"><h4>7</h4></div>
	<div class="item"><h4>8</h4></div>
	<div class="item"><h4>9</h4></div>
	<div class="item"><h4>10</h4></div>
	<div class="item"><h4>11</h4></div>
	<div class="item"><h4>12</h4></div>
</div>
<a class="button secondary play">Play</a>
<a class="button secondary stop">Stop</a>
{{#markdown }}
### Overview

Autoplay plugin has three options:
```
//default settings:

autoplay:false
autoplayTimeout:5000
autoplayHoverPause:false
```

In this example i've added two buttons with custom events for play and stop:

```
var owl = $('.owl-carousel');
owl.owlCarousel({
	items:4,
	loop:true,
	margin:10,
	autoplay:true,
	autoplayTimeout:1000,
	autoplayHoverPause:true
});

$('.play').on('click',function(){
	owl.trigger('play.owl.autoplay',[1000])
})

$('.stop').on('click',function(){
	owl.trigger('stop.owl.autoplay')
})
```

{{/markdown }} 

<script>
$(document).ready(function(){
	var owl = $('.owl-carousel');
	owl.owlCarousel({
		items:4,
		loop:true,
		margin:10,
		autoplay:true,
		autoplayTimeout:1000,
		autoplayHoverPause:true
	});

	$('.play').on('click',function(){
		owl.trigger('play.owl.autoplay',[1000])
	})

	$('.stop').on('click',function(){
		owl.trigger('stop.owl.autoplay')
	})
})
</script>
