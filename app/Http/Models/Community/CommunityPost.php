<?php

namespace App\Http\Models\Community;

use App\Http\Models\Community\CommunityGroup;
use App\Http\Models\Community\CommunityTag;
use App\Http\Models\Community\CommunityReport;
use App\Http\Models\Users;
use App\Http\Traits\Filterable;
use App\Base\Model\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommunityPost extends BaseModel
{
    use Filterable;
    use SoftDeletes;

    protected $table = 'community_posts';
    protected $fillable = [
        'content',
        'group_id',
        'data',
        'scheduled_posts',
        'user_id',
        'published_at',
        'pined_at'
    ];
    // protected $fillable = ['*'];

    public function group() {
        return $this->belongsTo(CommunityGroup::class, 'group_id');
    }

    public function user() {
        return $this->belongsTo(Users::class, 'user_id');
    }

    public function tags() {
        return $this->belongsToMany(CommunityTag::class, 'community_post_tags', 'post_id', 'tag_id');
    }

    public function reports() {
        return $this->hasMany(CommunityReport::class, 'post_id');
    }
}
