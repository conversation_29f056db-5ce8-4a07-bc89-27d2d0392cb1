<?php

namespace App\Listeners;

use App\Enums\AuditType;
use App\Events\PointsIncreased;
use App\Models\Achievement;
use App\Models\Level;

class PointsIncreasedListener
{
    public function __invoke(PointsIncreased $event): void
    {
        $type = $event->type;
        if (Level::count() === 0) {
            Level::add([
                'level' => config(key: 'level-up.starting_level'),
                'next_level_experience' => null,
            ]);
        }

        // Get the next level experience needed for the user's current level
        $nextLevel = Level::firstWhere(column: 'level', operator: '=', value: $event->user->getLevel() + 1);

        // Check if user's points are equal or greater than the next level's required experience
        if ($nextLevel && $event->user->getPoints() >= $nextLevel->next_level_experience) {
            // Find the highest level the user can achieve with current points
            $highestAchievableLevel = Level::query()
                ->where(column: 'next_level_experience', operator: '<=', value: $event->user->getPoints())
                ->orderByDesc(column: 'level')
                ->first();

            // Update the user level to the highest achievable level
            if ($highestAchievableLevel->level > $event->user->getLevel()) {
                $event->user->levelUp(to: $highestAchievableLevel->level);
            }
            $type = AuditType::LevelUp->value;
        }

        if (config(key: 'level-up.audit.enabled')) {
          $event->user->experienceHistory()->create([
            'lesson_id' => $event->lessonId,
            'points' => $event->pointsAdded,
            'type' => $type,
            'reason' => $event->reason,
            'reason_id' => $event->reasonId,
            'reason_table' => $event->reasonTable,
          ]);
        }

        // kích hoạt thành tích khi hoàn thành bài học chỉ định
        $lessonAchievements = Achievement::query()->where('trigger_condition', 'point')->where('trigger_value', '<=', $event->user->getPoints())->get();
        foreach($lessonAchievements as $achievement) {
          if (!$event->user->allAchievements()->find($achievement->id)) {
            $event->user->grantAchievement($achievement);
          }
        }
    }
}
