<?php

namespace App\Http\Controllers\Backend\Community;

use App\Base\Controller\BaseController;
use App\Exports\GroupChatExport;
use App\Exports\CommunityGroupExport;
use App\Http\Models\Community\CommunityGroup;
use App\Http\Models\Community\CommunityGroupTeacher;
use App\Http\Models\Course;
use App\Http\Models\CourseOwner;
use App\Http\Models\School\Salary;
use App\Services\Community\CommunityGroupScheduleService;
use App\Services\Community\CommunityGroupService;
use Carbon\Carbon;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Http\Models\School\SchoolUser;
use App\Http\Models\School\Department;
use App\Http\Models\School\CourseTeacher;
use App\Http\Models\Conversation;
use App\Http\ModelsFrontend\Users;
use App\Http\Models\Community\CommunityGroupUser;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class CommunityGroupController extends BaseController
{
    protected $communityGroupService;
    protected $communityGroupScheduleService;

    public function __construct(
        CommunityGroupService         $communityGroupService,
        CommunityGroupScheduleService $communityGroupScheduleService
    )
    {
        $this->communityGroupService = $communityGroupService;
        $this->communityGroupScheduleService = $communityGroupScheduleService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        //        return view('backend.community.groups');
    }

    public function users()
    {
        return view('backend.community.groups.users');
    }

    public function getUserList(Request $request)
    {
        $page = $request->page ? $request->page : 1;
        $perPage = $request->perPage ? $request->perPage : 10;
        $dataFilter = $request->all();
        $users = $this->communityGroupService->getUserList($page, $perPage, $dataFilter);
        return $this->statusOk($users);
    }

    public function getList(Request $request)
    {
        $page = $request->page ? $request->page : 1;
        $perPage = $request->perPage ? $request->perPage : 10;
        $dataFilter = $request->all();
        $groups = $this->communityGroupService->searchAndPaginate($page, $perPage, $dataFilter);
        return $this->statusOk($groups);
    }

    public function getAllGroup()
    {
        return $this->statusOK($this->communityGroupService->getAll());
    }

    public function getSampleList(Request $request)
    {
        $groups = $this->communityGroupService->getSampleList();
        return $this->statusOk($groups);
    }

    public function findById(Request $request, $id)
    {
        $group = $this->communityGroupService->findOne($id);
        return $this->statusOk($group);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'vip_level' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->statusNG($validator->errors());
        }
        $data = $request->all();
        if (!$request->is_sample) {
            $expired_at = isset($request->expired_at) && !empty($request->expired_at) ? $request->expired_at : null;
            $expired_at = Carbon::createFromFormat('Y-m-d', $request->expired_at);
            $result = $expired_at->lt(Carbon::now()->copy());
            if ($result) {
                return $this->statusNG(['msg' => 'Thời gian hết hạn cần lớn hơn hiện tại']);
            }

            $end_at = isset($request->end_at) && !empty($request->end_at) ? $request->end_at : null;
            $end_at = Carbon::createFromFormat('Y-m-d', $request->end_at);
            $result = $end_at->lt(Carbon::now()->copy());
            if ($result) {
                return $this->statusNG(['msg' => 'Thời gian hết hạn cần lớn hơn hiện tại']);
            }
        }

        $slug = Str::slug($request->name, '-');

        $data['slug'] = $slug;
        $data['start_time'] = $data['start_date'] . ' 00:00:00';

        $result = $this->communityGroupService->store($data);

        if ($data['teacher_id'] && $data['teacher_id'] != null) {
            $courseTeacher = new CourseTeacher();
            $courseTeacher->group_id = $result->id;
            $courseTeacher->teacher_id = $data['teacher_id'];
            $courseTeacher->created = \Carbon\Carbon::now()->copy();
            $courseTeacher->updated = \Carbon\Carbon::now()->copy();
            $courseTeacher->save();

            // bỏ tự tạo lương cho giáo viên
            $existedSalary = Salary::query()->where('user_id', $data['teacher_id'])->latest()->first();
            if (is_null($existedSalary)) {
                $newSalary = new Salary();
                $newSalary->group_id = $result->id;
                $newSalary->user_id = $data['teacher_id'];
                $newSalary->base_salary = 0;
                $newSalary->valid_from = $result->start_time;
                $newSalary->type = 1;
                $newSalary->level = $result->vip_level;
                $newSalary->currency = 'vnd';
                $newSalary->deleted_at = null;
                $newSalary->created_at = now();
                $newSalary->updated_at = now();
                $newSalary->save();
            } else {
                // check xem đã có lương của giáo viên đó trong group đó chưa
                $checkSalary = Salary::query()->where('user_id', $data['teacher_id'])->where('group_id', $result->id)->first();
                // nếu có rồi thì không tạo lương mới
                if (is_null($checkSalary)) {
                    $newSalary = new Salary();
                    $newSalary->group_id = $result->id;
                    $newSalary->user_id = $data['teacher_id'];
                    $newSalary->base_salary = $existedSalary->base_salary;
                    $newSalary->valid_from = $result->start_time;
                    $newSalary->type = $existedSalary->type;
                    $newSalary->level = $result->vip_level;
                    $newSalary->currency = $existedSalary->currency;
                    $newSalary->deleted_at = null;
                    $newSalary->created_at = now();
                    $newSalary->updated_at = now();
                    $newSalary->save();
                }
            }
        }

        if (!$request->is_sample && $request->isSampleGroupId) {
            $this->communityGroupService->clone($result->id, $request->isSampleGroupId);
        }

        // Auto <NAME_EMAIL> to this new group (516726)
        if ($result->type !== 'kaiwa') {
            $groupUser = new CommunityGroupUser();
            $groupUser->group_id = $result->id;
            $groupUser->user_id = 516726;
            $groupUser->save();
        }

        return $this->statusOk($result);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'expired_at' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->statusNG($validator->errors());
        }
        $data = $request->only([
            'name',
            'start_date',
            'end_at',
            'expired_at',
            'expired_after_days',
            'avatar',
            'banner',
            'vip_level',
            'size',
            'vip_session',
            'type',
            'shift_type',
            'shift_time',
            'links',
            'type_note',
            'note',
            'vip_combo_id'
        ]);

        $slug = Str::slug($request->name, '-');

        $data['slug'] = $slug;
        $data['start_time'] = $data['start_date'] . ' 00:00:00';

        $result = $this->communityGroupService->update($id, $data);

        if ($request->has('teacher_id')) {
            $courseTeacher = CourseTeacher::where('group_id', $id)->first();

            if ($courseTeacher) {
                $courseTeacher->teacher_id = $request->teacher_id;
                $courseTeacher->updated = \Carbon\Carbon::now()->copy();
                $courseTeacher->save();
            } else {
                $newcourseTeacher = new CourseTeacher();
                $newcourseTeacher->group_id = $id;
                $newcourseTeacher->teacher_id = $request->teacher_id;
                $newcourseTeacher->updated = \Carbon\Carbon::now()->copy();
                $newcourseTeacher->save();
            }
            $group = CommunityGroup::find($id);
            $existedSalary = Salary::query()->where('user_id', $request->teacher_id)->latest()->first();
            if (is_null($existedSalary)) {
                $newSalary = new Salary();
                $newSalary->group_id = $id;
                $newSalary->user_id = $request->teacher_id;
                $newSalary->base_salary = 0;
                $newSalary->valid_from = $group->start_time;
                $newSalary->type = 1;
                $newSalary->level = $group->vip_level;
                $newSalary->currency = 'vnd';
                $newSalary->deleted_at = null;
                $newSalary->created_at = now();
                $newSalary->updated_at = now();
                $newSalary->save();
            } else {
                // check xem đã có lương của giáo viên đó trong group đó chưa
                $checkSalary = Salary::query()->where('user_id', $request->teacher_id)->where('group_id', $id)->first();
                // nếu có rồi thì không tạo lương mới
                if (is_null($checkSalary)) {
                    $newSalary = new Salary();
                    $newSalary->group_id = $id;
                    $newSalary->user_id = $request->teacher_id;
                    $newSalary->base_salary = $existedSalary->base_salary;
                    $newSalary->valid_from = $group->start_time;
                    $newSalary->type = $existedSalary->type;
                    $newSalary->level = $group->vip_level;
                    $newSalary->currency = $existedSalary->currency;
                    $newSalary->deleted_at = null;
                    $newSalary->created_at = now();
                    $newSalary->updated_at = now();
                    $newSalary->save();
                }
            }
        }
        return $this->statusOk($result);
    }

    public function updateStatus(Request $request, $id)
    {
        $result = $this->communityGroupService->update($id, ['status' => $request->status]);
        return $this->statusOk($result);
    }

    public function softDelete($id)
    {
        $result = $this->communityGroupService->softDelete($id);
        return $this->statusOk($result);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function vip()
    {
        $vipGroups = CommunityGroup::query()
            ->where('name', 'LIKE', '%VIP%')
            ->get();
        foreach ($vipGroups as $group) {
            preg_match('/N\d/', $group->name, $m);
            $group->vip_level = $m[0];
            $group->save();
        }
        dd('done');
    }

    public function getTeacherList()
    {
        $depart = Department::query()
            ->whereIn('vip_level', ['N1', 'N2', 'N3', 'N4','N5', 'NTOKUTEI'])
            ->pluck('id');

        $model = SchoolUser::query()
            ->select('id', 'user_name', 'first_name', 'last_name', 'email')
            ->where('role', 2)
            ->whereIn('department_id', $depart)
            ->get();

        return response()->json($model);
    }

    public function createGroupChat(Request $request)
    {
        $group = CommunityGroup::find($request->id);

        $checkExistConversation = Conversation::where('group_id', $group->id)->first();

        if (isset($checkExistConversation)) {
            return response()->json([], 201);
        }

        $members = CommunityGroupUser::select('user_id')
            ->where('group_id', $group->id)
            ->get();

        if (in_array($group->type, ['vip500'])) {
            $userIds = $members->pluck('user_id')->toArray();
            $testers = Users::select('id')->whereIn('id', $userIds)->where('is_tester', true)->pluck('id')->toArray();
            $members = array_filter($userIds, function ($userId) use ($testers) {
                return !in_array($userId, $testers);
            });

            $teamChunkLength = [
                'vip500' => 15,
                'matgoc' => 10
            ];
            // Create team
            $teamChunk = array_chunk($members, $group->id == 2728 ? 70 : $teamChunkLength[$group->type]);

            // Each team has 15 members and all testers
            $records = [];
            foreach ($teamChunk as $key => $teamMembers) {
                $conversation = new Conversation();
                $conversation->creator_id = Auth::user()->id;
                $conversation->is_knowledge = 0;
                $conversation->is_online = 0;
                $conversation->is_group = 1;
                $conversation->group_id = $group->id;
                $conversation->title = '(' . ($key + 1) . ') ' . $request->name;
                $conversation->avatar = $group->avatar;
                $conversation->save();

                foreach ($teamMembers as $member) {
                    $conUser = [];
                    $conUser['user_id'] = $member;
                    $conUser['conversation_id'] = $conversation->id;
                    array_push($records, $conUser);
                }
                foreach ($testers as $member) {
                    $conUser = [];
                    $conUser['user_id'] = $member;
                    $conUser['conversation_id'] = $conversation->id;
                    array_push($records, $conUser);
                }
            }

            // $conversation = new Conversation();
            // $conversation->creator_id = Auth::user()->id;
            // $conversation->is_knowledge = 0;
            // $conversation->is_online = 0;
            // $conversation->is_group = 1;
            // $conversation->group_id = $group->id;
            // $conversation->title = $request->name;
            // $conversation->avatar = $group->avatar;
            // $conversation->save();

            // foreach ($members as $member) {
            //     $conUser = [];
            //     $conUser['user_id'] = $member;
            //     $conUser['conversation_id'] = $conversation->id;
            //     array_push($records, $conUser);
            // }
            // foreach ($testers as $member) {
            //     $conUser = [];
            //     $conUser['user_id'] = $member;
            //     $conUser['conversation_id'] = $conversation->id;
            //     array_push($records, $conUser);
            // }

            DB::table('conversation_users')->insert($records);
        } else {
            $conversation = new Conversation();
            $conversation->creator_id = Auth::user()->id;
            $conversation->is_knowledge = 0;
            $conversation->is_online = 0;
            $conversation->is_group = 1;
            $conversation->group_id = $group->id;
            $conversation->title = $request->name;
            $conversation->avatar = $group->avatar;
            $conversation->save();

            if (count($members) > 0) {
                $records = [];
                foreach ($members as $member) {
                    $conUser = [];
                    $conUser['user_id'] = $member->user_id;
                    $conUser['conversation_id'] = $conversation->id;
                    array_push($records, $conUser);
                }
                DB::table('conversation_users')->insert($records);
            }
        }

        $group->group_chat_id = $conversation->id;
        $group->save();

        return response()->json(1);
    }

    public function changeStatusLink(Request $request, $id)
    {
        $group = CommunityGroup::query()->where('id', $id)->first();

        if ($group) {
            $group->links = $request->update_links;
            $group->save();
            return $group->links;
        }

        return $this->statusNG(["message" => "Error please try again"]);
    }

    public function bulkActive(Request $request, $id)
    {
        $months = $request->get("months");
        $addCourseId = $request->get("addCourseId");
        $course = Course::find($addCourseId);
        $groupUsers = CommunityGroupUser::query()->where('group_id', $id)->pluck('user_id');
        $alreadyOwner = CourseOwner::query()->where('course_id', $addCourseId)->whereIn('owner_id', $groupUsers);
        $notOwner = array_diff($groupUsers->toArray(), $alreadyOwner->pluck('owner_id')->toArray());
        $activatedCourseUser = CommunityGroupUser::query()->whereJsonContains('activated_course', [$course->id])->pluck('user_id')->toArray();
        $group = CommunityGroup::find($id);
        $isFsoft = false;
        if (str_contains($group->name, 'FSOFT')) {
            $isFsoft = true;
        }
        $ownerArr = array_map(function ($user) use ($course, $months) {
            return [
                'owner_id' => $user,
                'course_id' => $course->id,
                'title' => $course->name,
                'watch_expired_day' => Carbon::now()->copy()->addMonths($months),
                'extra_days' => 0,
                'admin_active' => Auth::guard('admin')->user()->id,
                'admin_update' => Auth::guard('admin')->user()->id,
                'created_at' => now(),
                'updated_at' => now()
            ];
        }, $notOwner);
        CourseOwner::insert($ownerArr);
        CommunityGroupUser::query()->whereIn('user_id', $notOwner)->where('group_id', $id)->update([
            'activated_course' => [$course->id]
        ]);
        $alreadyOwner->chunkById(100, function ($ownerUsers) use ($months, $course, $activatedCourseUser, $id, $isFsoft) {
            foreach ($ownerUsers as $ownerUser) {
                $ownerUser->watch_expired_day = in_array($ownerUser->owner_id, $activatedCourseUser)
                    ? ($isFsoft
                        ? ($ownerUser->watch_expired_day < now()
                            ? Carbon::now()->copy()->addMonths($months)
                            : Carbon::parse($ownerUser->watch_expired_day)->copy()->addMonths($months)
                        )
                        : Carbon::parse($ownerUser->watch_expired_day))
                    : ($ownerUser->watch_expired_day < now()
                        ? Carbon::now()->copy()->addMonths($months)
                        : Carbon::parse($ownerUser->watch_expired_day)->copy()->addMonths($months));
                $ownerUser->save();
                $relation = CommunityGroupUser::query()->where('user_id', $ownerUser->owner_id)->where('group_id', $id)->first();
//          CommunityGroupUser::query()->where('user_id', $ownerUser->owner_id)->where('group_id', $id)->update([
//            'activated_course' =>  !is_null($relation->activated_course) && in_array($course->id, $relation->activated_course) ? $relation->activated_course : (is_null($relation->activated_course) ? [$course->id] : array_merge($relation->activated_course, [$course->id]))
//          ]);
                $relation->activated_course = !is_null($relation->activated_course) && in_array($course->id, $relation->activated_course) ? $relation->activated_course : (is_null($relation->activated_course) ? [$course->id] : array_merge($relation->activated_course, [$course->id]));
                $relation->save();
            }
        }, 'id');
        return $this->statusOK();
    }

    /**
     * @param $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportGroupChatMembers($id): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $conversations = Conversation::query()->with(['group_chat_users' => function ($q) use ($id) {
            $q->with(['group_user' => function ($q) use ($id) {
                $q->where('group_id', $id)->with(['invoice' => function ($q) {
                    $q->with(['sale', 'children' => function ($q) {
                        $q->select('id', 'paid_for_id', 'payment_status', 'invoice_status', 'price', 'discount_money', 'paid_money')->orderBy('id', 'desc');
                    }]);
                }]);
            }]);
        }])->where('group_id', $id)->get()->toArray();

        if (!count($conversations)) return 'Không có nhóm chat nào để xuất file';
        return Excel::download(new GroupChatExport($conversations), $this->slugify($conversations[0]['title']) . '.xlsx');
    }

    /**
     * @param $text
     * @param string $divider
     * @return string
     */
    private static function slugify($text, string $divider = '-'): string
    {
        // replace non letter or digits by divider
        $text = preg_replace('~[^\pL\d]+~u', $divider, $text);

        // transliterate
        $text = iconv('utf-8', 'utf-8//TRANSLIT', $text);

        // remove unwanted characters
        $text = preg_replace('~[^-\w]+~', '', $text);

        // trim
        $text = trim($text, $divider);

        // remove duplicate divider
        $text = preg_replace('~-+~', $divider, $text);

        // lowercase
        $text = strtolower($text);

        if (empty($text)) {
            return 'n-a';
        }

        return $text;
    }

    public function scheduleByGroups(Request $request)
    {
        $data = $this->communityGroupScheduleService->getGroupSchedules($request->ids);
        return $this->statusOK($data);
    }

    public function createSchedule(Request $request)
    {
        $data = $this->communityGroupScheduleService->create($request->all());
        return $this->statusOK($data->load('group'));
    }

    public function updateSchedule(Request $request, $id)
    {
        $this->communityGroupScheduleService->update($request->only('group_id', 'from', 'to', 'is_notice', 'note'), $id);
        $data = $this->communityGroupScheduleService->find($id);
        return $this->statusOK($data->load('group'));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateSchedule(Request $request): \Illuminate\Http\JsonResponse
    {
        $result = $this->communityGroupScheduleService->generateGroupSchedule($request->id);
        if ($result) {
            return $this->statusOK();
        } else {
            return $this->statusNG('Nhóm thiếu một trong các thông số Thời gian bắt đầu, kết thúc, ngày học hoặc ca học');
        }
    }

    public function deleteSchedule($id)
    {
        $this->communityGroupScheduleService->deleteSchedule($id);
        return $this->statusOK();
    }

    public function exportExcel(Request $request)
    {
        $data = CommunityGroup::query()
            ->select('id', 'name', 'start_date', 'start_time', 'vip_level', 'type', 'end_at', 'expired_at', 'status', 'shift_type', 'vip_session')
            ->with([
                'users' => function ($q) {
                    $q->select('users.id');
                },
                'teacher' => function ($q) use ($request) {
                    if (isset($request['teacher']) && $request['teacher'] != null) {
                        $q->where('teacher_id', $request['teacher']);
                    }
                    $q->select('id', 'group_id', 'course_id', 'teacher_id')
                        ->with(['teacher' => function ($q) {
                            $q->select('id', 'user_name');
                        }]);
                },
                'course_time' => function ($q) {
                    $q->select('id', 'course_id', 'group_id', 'date_attendance');
                }])
            ->where('id', '>', 6)
            ->where('is_sample', '=', $request['isSample'] === 'true' ? 1 : 0);

        if (isset($request['name']) && $request['name'] != "null" && $request != null) {
            $data->where('name', 'LIKE', '%' . $request['name'] . '%');
        }

        if (isset($request['vipLevel']) && $request['vipLevel'] != null) {
            $data->where('vip_level', '=', $request['vipLevel']);
        }

        if (isset($request['type']) && $request['type'] != null) {
            $data->whereIn('type', explode(',', $request['type']));
        }

        if (isset($request['status']) && $request['status'] != null) {
            $data->where('status', '=', $request['status']);
        }

        if (isset($request['shift_time']) && $request['shift_time'] != null) {
            $data->where('shift_time', '=', $request['shift_time']);
        }

        if (isset($request['start_date_from']) && $request['start_date_from'] != null) {
            $data->where('start_date', '>=', $request['start_date_from']);
        }

        if (isset($request['start_date_to']) && $request['start_date_to'] != null) {
            $data->where('start_date', '<=', $request['start_date_to']);
        }

        if (isset($request['start_date_order']) && $request['start_date_order'] != null) {
            $data = $data->orderBy('start_date', $request['start_date_order']);
        }

        if (isset($request['expiring']) && $request['expiring'] === 'true') {
            $data->where('expired_at', '>=', now());
            $data->where('expired_at', '<=', now()->clone()->addMonth());
        }

        if (isset($request['ended']) && $request['ended'] === 'true') {
            $data->where('end_at', '<=', now());
        }

        if (isset($request['shift_type']) && $request['shift_type'] != null) {
            $str = '';
            foreach (str_split($request['shift_type']) as $shiftType) {
                $str .= '%' . $shiftType . '%';
            }
            $data->where('shift_type', 'LIKE', $str);
        }

        if (isset($request['slot']) && $request['slot'] != null) {
            $data->whereRaw("size " . $request['slot'] . " (SELECT COUNT(id) FROM community_group_users WHERE community_group_users.group_id = community_groups.id)");
        }

        if (isset($request['teacher']) && $request['teacher'] != null) {
//            $data->whereRaw("exists (select (course_teachers_tbl.id) from `dungmorischool`.`course_teachers_tbl` where `community_groups`.`id` = `dungmorischool`.`course_teachers_tbl`.`group_id` and dungmorischool.course_teachers_tbl.teacher_id = " . $request['teacher'] . ")");
            $groupId = CommunityGroupTeacher::query()->where('teacher_id', $request['teacher'])->pluck('group_id');
            $data->whereIn('id', $groupId);
        }

        $data = $data->get();

        return Excel::download(new CommunityGroupExport($data, $request->month), 'community_group.xlsx');
    }

    public function updateStageGroup(Request $request, $id = null)
    {
        if (!$id) {
            return abort(404);
        }

        $group = CommunityGroup::query()->findOrFail($id);
        if ($group->stage == CommunityGroup::STAGE_GRAMMAR) {
            $this->communityGroupService->updateNameGroupChat($id, "(Nghe - Đọc - Hiểu)");
        }
        $stage = $group->stage == CommunityGroup::STAGE_GRAMMAR ? CommunityGroup::STAGE_LISTEN_READ_UNDERSTAND : CommunityGroup::STAGE_GRAMMAR;
        $group->fill(['stage' => $stage])->save();
        return $this->statusOK();
    }
}
