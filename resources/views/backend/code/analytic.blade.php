@extends('backend._default.dashboard')

@section('description') Quản lý Mã giới thiệu @stop
@section('keywords') jlpt result @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý Mã giới thiệu @stop

@section('assets')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/filterable_list.css') }}?{{filemtime('assets/backend/css/filterable_list.css')}}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
@stop

@section('content')
    <div class="filterable-list__screen" id="code__screen">
        <div class="filterable-list__list">
            <table>
                <thead>
                    <th>CODE</th>
                    <th>Name</th>
                    <th>Số lượt sử dụng</th>
                </thead>
                <tbody>
                {{-- <tr v-if="codes.length == 0">
                    <td colspan="12" class="text-center"> Không có dữ liệu</td>
                </tr> --}}
                    @foreach ($codes as $code)
                        <tr>
                            <td>
                                <div>{{ $code->code }}</div>
                            </td>
                            <td>
                                <div>{{ $code->name }}</div>
                            </td>
                            <td>
                                <div>{{ round($code->count) }}</div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    <script>
    </script>
@stop
