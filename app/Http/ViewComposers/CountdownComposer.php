<?php

namespace App\Http\ViewComposers;

use Illuminate\View\View;
use App\Http\Models\ThiThu\Exam;
use Carbon\Carbon;

class CountdownComposer {

    public function compose(View $view) {
        // $time = $exam->time_start;
        $time = '2020-07-05 09:00:00';
        $timeStart = Carbon::createFromFormat('Y-m-d H:i:s', $time);
        $now = Carbon::now();
        if($timeStart > $now) {
            $result = $now->diffInSeconds($timeStart);
            $timeStart = $timeStart->timestamp;
            $secondsInAMinute = 60;
            $secondsInAnHour  = 60 * $secondsInAMinute;
            $secondsInADay    = 24 * $secondsInAnHour;
            // extract days
            $days = formatNumber(floor($result / $secondsInADay));

            // extract hours
            $hourSeconds = $result % $secondsInADay;
            $hours = (string)formatNumber(floor($hourSeconds / $secondsInAnHour));

            // extract minutes
            $minuteSeconds = $hourSeconds % $secondsInAnHour;
            $minutes = (string)formatNumber(floor($minuteSeconds / $secondsInAMinute));

            // extract the remaining seconds
            $remainingSeconds = $minuteSeconds % $secondsInAMinute;
            $seconds = (string)formatNumber(ceil($remainingSeconds));
        }
        
        $view->with('days', $days)
        ->with('hours', $hours)
        ->with('minutes', $minutes)
        ->with('seconds', $seconds);
    }
}