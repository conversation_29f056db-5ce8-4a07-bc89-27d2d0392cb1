<?php
namespace App\CustomFilters;

use App\QueryFilter;

class CommunityGroupFilters extends QueryFilter
{
    public function id($id)
    {
        return $this->builder->where('id', $id);
    }

    public function name($name = '')
    {
        return $this->builder->where('name', 'LIKE', '%' . $name . '%');
    }

    public function vip_level($level)
    {
        if ($level == 'NKAIWA') {
            return $this->builder->whereIn('vip_level', ['kaiwacb', 'kaiwasc', 'kaiwatc', 'kaiwatc2', 'kaiwanc']);
        }
        if ($level == 'Ntokutei') {
            return $this->builder->whereIn('vip_level', ['tokutei1', 'tokutei2']);
        }
        return $this->builder->where('vip_level', $level);
    }

    public function type($id)
    {
        return $this->builder->where('type', $id);
    }

    public function shift_time($time)
    {
        return $this->builder->whereTime('shift_time', $time);
    }
}

?>
