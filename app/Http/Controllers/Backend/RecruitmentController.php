<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\ModelsFrontend\Recruitment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class RecruitmentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function getPage()
    {
        $posts = Recruitment::orderBy('order', 'asc')->get();
        return view('backend.recruitment.index')
            ->with("posts", $posts);
    }

    public function create(Request $request)
    {


        $post = new Recruitment();
        $post->title = $request->title;
        $post->salary = $request->salary;
        $post->place = $request->place;
        $post->link = $request->link;
        $post->order = 0;
        $post->public = $request->public;
        $post->save();

        Recruitment::increment('order');

        return response()->json($post);
    }

    public function edit(Request $request)
    {
        $post = Recruitment::find($request->id);
        $post->title = $request->title;
        $post->salary = $request->salary;
        $post->place = $request->place;
        $post->link = $request->link;
        $post->order = $request->order;
        $post->public = $request->public;
        $post->save();
        return response()->json($post);
    }

    public function delete(Request $request)
    {
        Recruitment::find($request->id)->delete();
        return response()->json();
    }

    public function sort(Request $request)
    {
        Log::info($request);
        $post = Recruitment::find($request->id);
        $post->order = $request->sort_order;
        $post->save();
        return response()->json($post);
    }
}
