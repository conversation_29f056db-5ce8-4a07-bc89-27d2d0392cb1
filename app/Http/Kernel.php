<?php
namespace App\Http;

use App\Http\Middleware\CustomCampaign;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        \Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
//         LogRequestResponse::class
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web'    => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
//             \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\WebAuth::class,
            \App\Http\Middleware\AdminActivity::class,
            CustomCampaign::class,
        ],
        'vendor' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\AdminActivity::class,
        ],
        'api'    => [
            'throttle:60,1',
            'bindings',
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth'         => \Illuminate\Auth\Middleware\Authenticate::class,
        'auth.basic'   => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'bindings'     => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'can'          => \Illuminate\Auth\Middleware\Authorize::class,
        'guest'        => \App\Http\Middleware\RedirectIfAuth::class,
        'throttle'     => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'signed'       => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'check.survey' => \App\Http\Middleware\CheckSurvey::class,
        'role'         => \App\Http\Middleware\RoleMiddleware::class,
        'ai.agent'     => \App\Http\Middleware\AiAgentMiddleware::class,
    ];
}
