.pc-header {
  background-color: rgba(255, 255, 255, 0.5);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.pc-header .container {
  max-width: 100%;
  width: 90%;
}
.pc-header .header-menu {
  display: none;
}
.pc-header .stage {
  display: none;
}
.pc-header .leaf {
  display: none;
}
.pc-header .logo1 {
  display: none;
}
.pc-header .logo2 {
  display: block !important;
}
.pc-header .logo2 img {
  width: 176px;
}
.pc-header .icon-color {
  color: rgb(7, 58, 59);
}
.pc-header .header-notification .dropdown-toggle-icon {
  color: rgb(7, 58, 59);
}
.pc-header .auth-button {
  padding-left: 0;
  border: none;
  background: transparent;
  font-family: "Beanbag_Dungmori_Rounded";
}
.pc-header .auth-button span {
  display: none;
}
.pc-header .auth-button #text-login,
.pc-header .auth-button #text-register {
  color: #07403f;
  font-size: 16px;
}
.pc-header .auth-button #text-register {
  margin-left: 10px;
  height: 38px;
  width: 152px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: white;
  background: #ec6e23;
  border-radius: 9999px;
}

.mobile-header {
  padding-top: 0;
  background-color: rgba(255, 255, 255, 0.5) !important;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.mobile-header #nav-icon {
  display: none;
}
.mobile-header #logo {
  display: none;
}
.mobile-header .logo3 {
  display: block !important;
}
.mobile-header .logo3 img {
  width: 45px;
}
.mobile-header .icon-color {
  color: rgb(7, 58, 59);
}
.mobile-header #account-container {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
  -webkit-justify-content: end;
      -ms-flex-pack: end;
          justify-content: end;
}
.mobile-header .login-button {
  color: #07403f;
  font-size: 16px;
  background-color: transparent;
  border: none;
}
.mobile-header .register-button {
  border: none;
  color: #07403f;
  font-size: 12px;
  height: 32px;
  width: 112px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: white;
  background: #ec6e23;
  border-radius: 9999px;
}
.mobile-header .btn-store {
  display: inline-block !important;
}
.mobile-header .svgIcon--bell {
  display: none;
}
.mobile-header #auth-container {
  margin-top: 0 !important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}
.mobile-header #auth-container .btn-profile {
  display: block !important;
}
.mobile-header .messenger-icon .stroke-current {
  color: rgb(7, 58, 59);
}

.header-avatar .dropdown-toggle,
.header-avatar .dropdown-menu {
  display: none;
}
.header-avatar .btn-profile {
  display: block !important;
}
@media screen and (min-width: 1024px) {
  .header-avatar .btn-profile .menu {
    right: 4.2%;
  }
}

.course-basic .video-js {
  max-width: 100%;
  width: 100%;
}
.course-basic .video-js video {
  width: 100% !important;
}
.course-basic .video-js .vjs-poster img {
  max-width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.course-basic .tutorial .course-step {
  visibility: hidden;
}
.course-basic .tutorial .course-step.step-4 {
  -webkit-transition-delay: 200ms;
          transition-delay: 200ms;
}
.course-basic .tutorial .course-step.active {
  visibility: visible;
}
.course-basic .stages .stage .head {
  background-color: #e6e6e6;
}
.course-basic .stages .stage .head .arrow-icon {
  position: relative;
  top: 2px;
}
.course-basic .stages .stage .head .learning {
  display: none;
}
.course-basic .stages .stage .head.is-open {
  background-color: #c1eaca;
}
.course-basic .stages .stage .head.is-open .arrow-icon {
  -webkit-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
          transform: rotate(180deg);
  top: 0;
}
.course-basic .stages .stage .head.is-open .learning {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.course-basic .stages .stage .head.is-open .schedule {
  display: none;
}
.course-basic .lesson-progress {
  position: relative;
}
.course-basic .lesson-progress::after {
  content: "";
  display: block;
  position: absolute;
  left: -3px;
  top: -3px;
  right: -3px;
  bottom: -3px;
  background-color: transparent;
  background-image: conic-gradient(orange, orange var(--data-percent), transparent var(--data-percent));
  z-index: -100;
  border-radius: 21.5%;
}

.lesson-tutorial .lesson-step {
  visibility: hidden;
}
.lesson-tutorial .lesson-step.active {
  visibility: visible;
}

.lesson-group-item .group-list {
  display: none;
}
.lesson-group-item.is-open .group-list {
  display: grid;
}
.lesson-group-item.is-open .arrow-icon {
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
          transform: rotate(90deg);
}

.tooltip-custom__wrapper {
  position: relative;
}
.tooltip-custom__wrapper .tooltip-custom {
  display: none;
  font-size: 16px;
  color: #07403f;
  background: #c1eaca;
  white-space: nowrap;
  padding: 3px 13px;
  border-radius: 5px;
}
.tooltip-custom__wrapper:hover .tooltip-custom {
  display: block;
}

.lesson .video .video-js {
  width: 100%;
  position: relative;
  padding-top: 56.3%;
}
.lesson .server-video.active {
  background: #c1eaca;
}
.lesson .quality-video.active {
  background: #d9d9d9;
}

#lesson-list .btn-show-menu .tooltip-menu {
  right: -200px;
}
#lesson-list .btn-show-menu:hover .tooltip-menu {
  right: 85px;
}

#lesson-basic-container.training .menu-content {
  outline: rgba(0, 0, 0, 0.9) solid 5000px;
}
#lesson-basic-container.training .overlay-tutorial {
  height: 80px !important;
}

#lesson-main.menu-hidden .lesson-main-container {
  margin-left: auto;
  margin-right: auto;
}

.group-list .show-arrow .lock-icon {
  display: none;
}
.group-list .show-lock .arrow-group {
  display: none;
}
.group-list .lessons {
  display: none;
}
.group-list.is-open .arrow-group {
  rotate: 90deg;
  display: block;
}
.group-list.is-open .lessons {
  display: block;
}
.group-list.is-open .lock-icon {
  display: none;
}

.require-icon {
  position: relative;
  top: -5px;
}
.require-icon.text-over {
  position: absolute;
  right: 0;
  top: 0;
}

.btn-download .download-icon {
  position: relative;
}
.btn-download .download-icon.downloaded::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  bottom: 0;
  right: 0;
  background-image: url("/images/icons/done.png");
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

@-webkit-keyframes shadow-first-lesson {
  0% {
    -webkit-box-shadow: 0 0 25px 0 var(--data-first-lesson-color);
            box-shadow: 0 0 25px 0 var(--data-first-lesson-color);
  }
  50% {
    -webkit-box-shadow: 0 0 9px 0 var(--data-first-lesson-color);
            box-shadow: 0 0 9px 0 var(--data-first-lesson-color);
  }
  to {
    -webkit-box-shadow: 0 0 25px 0 var(--data-first-lesson-color);
            box-shadow: 0 0 25px 0 var(--data-first-lesson-color);
  }
}

@keyframes shadow-first-lesson {
  0% {
    -webkit-box-shadow: 0 0 25px 0 var(--data-first-lesson-color);
            box-shadow: 0 0 25px 0 var(--data-first-lesson-color);
  }
  50% {
    -webkit-box-shadow: 0 0 9px 0 var(--data-first-lesson-color);
            box-shadow: 0 0 9px 0 var(--data-first-lesson-color);
  }
  to {
    -webkit-box-shadow: 0 0 25px 0 var(--data-first-lesson-color);
            box-shadow: 0 0 25px 0 var(--data-first-lesson-color);
  }
}
.first-lesson {
  -webkit-animation: shadow-first-lesson 1s infinite;
          animation: shadow-first-lesson 1s infinite;
}
