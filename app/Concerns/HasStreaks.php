<?php

namespace App\Concerns;

use App\Enums\AuditType;
use App\Enums\ReasonTable;
use App\Http\ModelsFrontend\LessonProgress;
use App\Models\Achievement;
use App\Models\ExperienceAudit;
use App\Models\StreakRule;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Event;
use App\Events\StreakBroken;
use App\Events\StreakFrozen;
use App\Events\StreakIncreased;
use App\Events\StreakStarted;
use App\Events\StreakUnfroze;
use App\Models\Activity;
use App\Models\Streak;
use App\Models\StreakHistory;

trait HasStreaks
{
    public function recordStreak(Activity $activity): void
    {
        // If the user doesn't have a streak for this activity, start a new one
        if (! $this->hasStreakForActivity(activity: $activity)) {
            $this->startNewStreak($activity);
            $this->addStreakAchievement('login_count');
            return;
        }

        $lastActivity = $this->getStreakLastActivity($activity);
        $diffInDays = $lastActivity
            ->activity_at
            ->startOfDay()
            ->diffInDays(now()->startOfDay());

        // Checking to see if the streak is frozen
        // chuỗi gần nhất có frozen_until (có bảo vệ chuỗi)
        if ($lastActivity->frozen_until) {
            // học ngày 1, 2 - mua bảo vệ 2 ngày tối ngày 2 - bảo vệ cho ngày 3 và 4

            // frozen_until >= hiện tại --> user truy cập trong thời gian hiệu lực của bảo vệ. Chia làm 2 trường hợp truy cập trang
            if (now()->lessThanOrEqualTo($lastActivity->frozen_until->endOfDay())) {
              // vẫn đang ở ngày mua -> bỏ qua
              if ($diffInDays === 0) {
                return;
              }
              // ở ngày 1 hoặc ngày 2, cộng số ngày bằng với diffInDays
              else {
                $streak = $this->streaks()->whereBelongsTo(related: $activity);
                $streak->increment(column: 'count', amount: $diffInDays);
                $streak->update(values: ['activity_at' => now()]);

                event(new StreakIncreased($this, $activity, $streak->first()));
              }
              if (now()->greaterThanOrEqualTo($lastActivity->frozen_until)) {
                $this->unFreezeStreak($activity);
              }
            }
            // frozen_until < hiện tại --> user truy cập sau thời gian hiệu lực của bảo vệ -> reset chuỗi nhưng cộng thêm số ngày bằng chênh lệch ngày giữa frozen_until và activity_at ngày vào lịch sử chuỗi
            else {
              $streak = $this->streaks()->whereBelongsTo(related: $activity);
              $protectedDays = $lastActivity
                ->activity_at
                ->startOfDay()
                ->diffInDays($lastActivity->frozen_until->startOfDay()) - 1;
              $this->resetStreak($activity, $protectedDays);
              event(new StreakBroken($this, $activity, $streak->first()));

              return;
            }

            return;
        }

        if ($diffInDays === 0) {
            return;
        }

        // Check to see if the streak was broken
        if ($diffInDays > 1) {
            $streak = $this->streaks()->whereBelongsTo(related: $activity);
            event(new StreakIncreased($this, $activity, $streak->first()));

            $this->resetStreak($activity);

            event(new StreakBroken($this, $activity, $streak->first()));

            return;
        }

        if ($diffInDays === 1) {
            $streak = $this->streaks()->whereBelongsTo(related: $activity);
            $streak->increment(column: 'count');
            $streak->update(values: ['activity_at' => now()]);

            event(new StreakIncreased($this, $activity, $streak->first()));
        } else {
            $this->startNewStreak($activity);
        }
    }

    public function recordLessonStreak(Activity $activity): void
    {
      if (! $this->hasStreakForActivity(activity: $activity)) {
        $this->startNewStreak($activity);
        $this->addStreakAchievement('lesson_count');
        return;
      }

      $diffInDays = $this->getStreakLastActivity($activity)
        ->activity_at
        ->startOfDay()
        ->diffInDays(now()->startOfDay());

      if ($diffInDays !== 0) {
        $this->startNewStreak($activity);
      } else {
        $count = $this->todayLessonStreakCount();
        $streak = $this->streaks()->whereBelongsTo(related: $activity);
        $streak->update(values: [
          'count' => $count,
          'activity_at' => now()
        ]);

        event(new StreakIncreased($this, $activity, $streak->first()));
      }
    }

    public function addStreakAchievement(string $type, int $count = 1): void
    {
      // kích hoạt thành tích cho chuỗi bài học
      $loginAchievements = Achievement::query()->where('trigger_condition', $type)->where('trigger_value', '<=', $count)->get();
      foreach($loginAchievements as $achievement) {
        if (!$this->allAchievements()->find($achievement->id)) {
          $this->grantAchievement($achievement);
        }
      }
    }

    public function hasStreakAwardToday(int $ruleId): bool
    {
      return ExperienceAudit::query()->where('user_id', $this->id)->where('reason_table', 'experience_rules')->where('reason_id', $ruleId)->whereDate('created_at', now())->exists();
    }

    protected function hasStreakForActivity(Activity $activity): bool
    {
        return $this->streaks()
            ->whereBelongsTo(related: $activity)
            ->exists();
    }

    public function streaks(): HasMany
    {
        return $this->hasMany(related: Streak::class);
    }

    protected function startNewStreak(Activity $activity): Model|Streak
    {
        $streak = $activity->streaks()
            ->updateOrCreate([
                'user_id' => $this->id,
                'activity_id' => $activity->id,
            ],
            [
              'activity_at' => now(),
            ]
            );

        event(new StreakStarted($this, $activity, $streak));

        return $streak;
    }

    protected function getStreakLastActivity(Activity $activity): Streak
    {
        return $this->streaks()
            ->whereBelongsTo(related: $activity)
            ->latest(column: 'activity_at')
            ->first();
    }

    public function resetStreak(Activity $activity, int $protectedDays = 0): void
    {
        // Archive the streak
        if (config(key: 'level-up.archive_streak_history.enabled')) {
            $this->archiveStreak($activity, $protectedDays);
        }

        $this->streaks()
            ->whereBelongsTo(related: $activity)
            ->update([
                'count' => 1,
                'activity_at' => now(),
                'frozen_until' => null
            ]);
    }

    protected function archiveStreak(Activity $activity, int $protectedDays = 0): void
    {
        $latestStreak = $this->getStreakLastActivity($activity);

        if ($protectedDays) {
          $latestStreak->increment(column: 'count', amount: $protectedDays);
          $latestStreak->activity_at = $latestStreak->activity_at->addDays($protectedDays);
          $latestStreak->save();

          event(new StreakIncreased($this, $activity, $latestStreak));
        }

        StreakHistory::create([
            'user_id' => $this->id,
            'activity_id' => $activity->id,
            'count' => $latestStreak->count,
            'started_at' => $latestStreak->activity_at->subDays($latestStreak->count - 1),
            'ended_at' => $latestStreak->activity_at,
        ]);
    }

  /**
   * @return HasMany
   */
    public function streakHistories(): HasMany
    {
      return $this->hasMany(related: StreakHistory::class);
    }

  /**
   * @param Activity $activity
   * @return \Illuminate\Database\Eloquent\Collection
   */
    public function getStreakHistory(Activity $activity): \Illuminate\Database\Eloquent\Collection
    {
      return $this->streakHistories()
        ->whereBelongsTo(related: $activity)
        ->get();
    }

    public function getCurrentStreakCount(Activity $activity): int
    {
        $streak = $this->streaks()->whereBelongsTo(related: $activity)->first();
        return $streak->count ?? 0;
    }

  /**
   * @param Activity $activity
   * @return Model|HasMany|null
   */
    public function getCurrentStreak(Activity $activity): Model|HasMany|null
    {
      return $this->streaks()->whereBelongsTo(related: $activity)->first();
    }

    public function hasStreakToday(Activity $activity): bool
    {
        return $this->getStreakLastActivity($activity)
            ->activity_at
            ->isToday();
    }

    public function freezeStreak(Activity $activity, int $days = null): bool
    {
        $days = $days ?? config(key: 'level-up.freeze_duration');

        Event::dispatch(new StreakFrozen(
            frozenStreakLength: $days,
            frozenUntil: now()->addDays(value: $days)->startOfDay()
        ));

        return $this->getStreakLastActivity($activity)
            ->update(['frozen_until' => now()->addDays(value: $days)->startOfDay()]);
    }

    public function unFreezeStreak(Activity $activity): bool
    {
        Event::dispatch(new StreakUnfroze());

        return $this->getStreakLastActivity($activity)
            ->update(['frozen_until' => null]);
    }

    public function isStreakFrozen(Activity $activity): bool
    {
        return ! is_null($this->getStreakLastActivity($activity)->frozen_until);
    }

    public function todayLessonStreakCount()
    {
      return LessonProgress::query()
        ->where('user_id', $this->id)
        ->whereNotNull('lesson_id')
        ->whereDate('finished_at', now())
        ->count('lesson_id');
    }
}
