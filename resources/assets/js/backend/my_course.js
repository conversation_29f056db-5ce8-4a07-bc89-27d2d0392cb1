var myCoursesVue = new Vue({
  el: '#my-course-content',
  data: {
    myCourses: [],
    user: userCurrent
  },
  methods: {
    getMyCourses() {
      var vm = this;

      if (vm.myCourses.length > 0) {
        return;
      }

      $.post(window.location.origin + '/my-courses', {
        _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      }, function (data) {
        var userHasAnyCourse = false;
        if (data.length > 0) {
          userHasAnyCourse = true;
        }
        for (var i = 0; i < data.length; i++) {
          data[i].expired_day = moment(data[i].watch_expired_day).format("DD/MM/YYYY");
          // if (moment(data[i].expired_day, "DD/MM/YYYY").isAfter(moment("31/08/2024", "DD/MM/YYYY"))) {
          //   userHasAnyCourse = true;
          // }
        }
        // var lastOpened = localStorage.getItem("jlpt-gift-opened");

        const today = new Date().toISOString().split("T")[0];

        const lastOpenedDate = localStorage.getItem("modal-event-last-opened");
        if (userHasAnyCourse && lastOpenedDate !== today) {
          $("#modalEvent").modal("show");
        }
        if (lastOpenedDate !== today) {
          localStorage.setItem("modal-event-last-opened", today);
        }

        // if (!lastOpened && userHasAnyCourse && moment().isSameOrAfter(moment("20/01/2025", "DD/MM/YYYY")) && vm.user.id === 349661) {
        //   $.fancybox.open({
        //     src: "#jlpt-gift-container",
        //     type: "inline",
        //     opts: {
        //       touch: false
        //     }
        //   });
        //
        //   localStorage.setItem('jlpt-gift-opened', '1');
        // }
        vm.myCourses = data;
        $('#my-course-content').css('display', vm.myCourses.length > 0 ? 'grid' : 'block');
        $('#my-course-content').css('overflow-y', vm.myCourses.length > 4 ? 'scroll' : 'auto');
      });
    },
  },
  mounted() {
    this.getMyCourses();
  }
});

$(document).keydown(function(e) {
  // ESCAPE key pressed
  if (e.keyCode == 27) {
    myCourseClick(true);
  }
});
