/**
 * @license Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */
CKEDITOR.plugins.setLang( 'filetools', 'es', {
	loadError: 'Ha ocurrido un error durante la lectura del archivo.',
	networkError: 'Error de red ocurrido durante carga de archivo.',
	httpError404: 'Un error HTTP ha ocurrido durante la carga del archivo (404: Archivo no encontrado).',
	httpError403: 'Un error HTTP ha ocurrido durante la carga del archivo (403: Prohibido).',
	httpError: 'Error HTTP ocurrido durante la carga del archivo (Estado del error: %1).',
	noUrlError: 'URL cargada no está definida.',
	responseError: 'Respueta del servidor incorrecta.'
} );
