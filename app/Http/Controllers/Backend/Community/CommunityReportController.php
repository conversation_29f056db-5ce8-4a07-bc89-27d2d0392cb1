<?php

namespace App\Http\Controllers\Backend\Community;

use App\Base\Controller\BaseController;
use App\Services\Community\CommunityReportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CommunityReportController extends BaseController
{
    protected $CommunityReportService;

    public function __construct(
        CommunityReportService $CommunityReportService
    ){
        $this->CommunityReportService = $CommunityReportService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        return view('backend.community.reports.index');
    }

    public function count() {
        $total = $this->CommunityReportService->count();
        return $this->statusOk(['total' => $total]);
    }
    public function getList(Request $request)
    {
        $page = $request->page ? $request->page : 1;
        $perPage = $request->perPage ? $request->perPage : 10;
        $dataFilter = $request->all();
        $items = $this->CommunityReportService->searchAndPaginate($page, $perPage, $dataFilter);
        return $this->statusOk($items);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
		$validator = Validator::make($request->all(), [
            'content' => 'required',
            'group_id' => 'required'
        ]);

		if ($validator->fails()) {
            return $this->statusNG($validator->errors());
        }    
        $data = $request->all();
        $data['user_id'] = 1;
        $result = $this->CommunityPostService->store($data);  
        return $this->statusOk($result);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
		$validator = Validator::make($request->all(), [
            'content' => 'required'
        ]);

		if ($validator->fails()) {
            return $this->statusNG($validator->errors());
        }    
        $data = $request->only(['content']);
        $result = $this->CommunityPostService->update($id, $data); 
        return $this->statusOk($result);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $result = $this->CommunityPostService->delete($id);
        return $this->statusOk($result);
    }
}
