@media only screen and (max-width: 767px) {
  .main {
    width: 100%;
    margin-top: 50px;
  }
  .main .main-center {
    width: 100%;
  }
  .main .main-center .main-user-left {
    background-color: #EEE;
  }
  .main .main-center .main-user-left {
    width: 100%;
    min-height: auto;
    float: none;
    padding: 0;
  }
  .main .main-center .main-user-left .clear-preview-upload {
    margin-top: 4px;
    margin-left: calc((100vw - 125px)/2);
  }
  .main .main-center .main-user-left .change-avatar {
    margin: 10px 0 10px 0;
    width: 105px;
    margin-left: calc((100vw - 110px)/2);
  }
  .main .main-center .main-user-left .change-avatar .change-avatar-btn-mobile {
    display: inline;
  }
  .main .main-center .main-user-left .change-avatar .change-avatar-btn-desktop {
    display: none;
  }
  .main .main-center .main-user-left .save-avatar {
    display: inline;
    margin: 10px 0 10px 0;
    width: 105px;
    margin-left: calc((100vw - 110px)/2);
  }
  .main .main-center .main-user-left .account-left-menu {
    float: left;
    width: 100%;
    padding: 0 15px;
    border-bottom: 1px solid #EEE;
  }
  .main .main-center .main-user-left .account-left-menu .item-user {
    width: 20%;
    margin-top: 8px;
    text-align: center;
  }
  .main .main-center .main-user-left .account-left-menu .item-user i {
    font-size: 20px;
  }
  .main .main-center .main-user-left .account-left-menu .item-user span {
    display: none;
  }
  .main .main-center .main-user-left .current {
    border-bottom: 2px solid #588d3f;
  }
  .main .main-center .main-user-left .logout-tab {
    display: none;
  }
  .main .main-center .main-user-left .user-avatar-container .user-avatar {
    width: 105px;
    height: 105px;
    margin-left: calc((100vw - 110px)/2);
    margin-top: 15px;
  }
  .main .main-center .main-user-right {
    width: 100%;
    padding: 0;
    min-height: 550px;
    padding: 0 15px;
  }
  .main .main-center .main-user-right #user-info-table {
    width: 100%;
  }
  .main .main-center .main-user-right .captcha-active-course {
    margin-top: 5px;
  }
  .main .main-center .main-user-right .change-captcha-icon {
    margin-top: 18px;
  }
  .main .main-center .main-user-right .table-box {
    width: 100%;
    overflow-x: auto;
  }
  .main .main-center .main-user-right .captcha-container {
    margin-left: 0;
  }
  .main .main-center .main-user-right .user-form-item .user-form-input {
    min-width: auto;
  }
  .main .main-center .main-user-right input, .main .main-center .main-user-right select, .main .main-center .main-user-right textarea {
    border: 1px solid #DDD;
  }
  .main .main-center .main-user-right .main-user-title {
    padding-bottom: 15px;
    font-size: 20px;
    width: 100%;
  }
  .main .main-center .main-user-right .mark-as-readed {
    padding-top: 5px;
    padding-bottom: 15px;
  }
  .main .main-center .main-user-right .dm-tab {
    height: auto;
  }
  .main .main-center .main-user-right .dm-tab .tab-item {
    text-align: center;
    height: auto;
    width: 45%;
    margin: 0;
    padding: 5px;
    border: 0;
  }
  .main .main-center .main-user-right .dm-tab .tab-item i {
    display: none;
  }
  .main .main-center .main-user-right .dm-tab .review-more-courses {
    width: 100%;
  }
  .main .main-center .main-user-right .tab-content-container .bought-courses-container {
    width: 100%;
  }
  .main .main-center .main-user-right .tab-content-container .bought-courses-container .notification-empty-container {
    padding: 130px 0;
  }
  .main .main-center .main-user-right .tab-content-container .bought-courses-container .course-item {
    width: 47%;
    margin: 0 3px 10px 3px;
  }
  .main .main-center .main-user-right .tab-content-container .bought-courses-container .course-item .images {
    width: 100%;
  }
  .main .main-center .main-user-right .tab-content-container .bought-courses-container .course-item .images a img {
    width: 100%;
  }
  .main .main-center .main-user-right .notification-item .user-form-item {
    width: calc(100vw - 70px);
    padding-left: 20px;
    padding-right: 0;
  }
  .main .main-center .main-user-right .notification-item .user-form-item i {
    margin-left: -20px;
  }
  .main .main-center .main-user-right .notification-item .notifi-info {
    width: 70px;
    padding-left: 10px;
  }
  .main .main-center .main-user-right .bought-courses {
    margin-top: 30px;
  }
  .main .main-center .main-user-right .bought-other-courses {
    margin-bottom: 20px;
  }
  .main .main-center .main-user-right .paginate-container {
    width: 100%;
  }
  .main .main-center .main-user-right .course-test #reviewTestResult .test-modal-dialog {
    width: auto;
  }
  .main .main-center .main-user-right .course-test #reviewTestResult .question-box .answer-box div:last-child hr {
    top: -15px;
  }
  .main .main-center .main-user-right .course-test #reviewTestResult .question-box .answer-box .three-line-answer {
    height: auto;
  }
  .main .main-center .main-user-right .course-test #reviewTestResult .question-box .answer-box .two-line-answer {
    height: auto;
  }
  .main .main-center .main-user-right .course-test .review-test-result {
    position: static;
    margin-top: 10px;
    float: none;
  }
  .main .main-center .main-user-right .course-test .remove-test-result {
    position: static;
    margin-top: 10px;
  }
}

@media only screen and (max-width: 370px) {
  .main .main-center .main-user-right #user-info-table .form-text-email {
    font-size: 11px;
  }
}

@media only screen and (max-width: 767px) {
  body {
    width: 100%;
    float: left;
    overflow-x: hidden;
  }
  .site-header .header-content {
    position: fixed;
    height: 50px;
    border-bottom: 1px solid #CCC;
  }
  .site-header .header-content #search-input-mobile {
    float: right;
    margin-right: 5px;
  }
  .site-header .header-content #search-input-mobile .search-input-mobile {
    width: calc(100vw - 108px);
    margin: 10px 5px 0 5px;
    height: 28px;
    text-indent: 5px;
    border-radius: 3px;
    border: 1px solid #CCC;
  }
  .site-header .header-content #search-input-mobile .close-icon {
    color: #e74c3c;
    padding: 10px;
    font-size: 15px;
  }
  .site-header .header-content .container {
    width: 100%;
  }
  .site-header .header-content .container #nav-icon {
    margin-left: 8px;
    width: 45px;
    height: 45px;
    float: left;
    transform: rotate(0deg);
    transition: .1s ease-in-out;
    cursor: pointer;
  }
  .site-header .header-content .container #nav-icon .nav-span {
    display: block;
    position: absolute;
    height: 2px;
    width: 18px;
    background: #588d3f;
    border-radius: 2px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: .25s ease-in-out;
  }
  .site-header .header-content .container #nav-icon .nav-span:nth-child(1) {
    left: 9px;
    top: 16px;
    transform-origin: left center;
  }
  .site-header .header-content .container #nav-icon .nav-span:nth-child(2) {
    left: 9px;
    top: 22px;
    transform-origin: left center;
  }
  .site-header .header-content .container #nav-icon .nav-span:nth-child(3) {
    left: 9px;
    top: 28px;
    transform-origin: left center;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu {
    margin-top: 4px;
    margin-left: -9px;
    width: calc((100vw)*0.85);
    max-width: calc(100vw);
    font-size: 16px;
    padding-bottom: 10px;
    border-top-left-radius: 0;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu li {
    padding: 7px 0;
    border-bottom: 1px solid #eee;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu li a {
    color: #333;
    padding-left: 15px;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu li a p {
    margin-bottom: 0;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu li:last-child {
    border: 0;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu .courses-detail-list {
    padding: 15px 15px;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu .courses-detail-list .course-list-table {
    font-size: 12px;
    text-align: center;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu .courses-detail-list .course-list-table td {
    border: 1px solid #82ae1d;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu .courses-detail-list table {
    margin-bottom: 0;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu .courses-detail-list .course-list-table td {
    padding: 12px;
  }
  .site-header .header-content .container #nav-icon .dropdown-menu .icon-menu-item {
    float: left;
    color: #82ae1d;
    font-size: 19px;
    width: 23px;
  }
  .site-header .header-content .container #nav-icon.open span:nth-child(1) {
    transform: rotate(45deg);
    top: 16px;
    left: 13px;
  }
  .site-header .header-content .container #nav-icon.open span:nth-child(2) {
    width: 0;
    opacity: 0;
  }
  .site-header .header-content .container #nav-icon.open span:nth-child(3) {
    transform: rotate(-45deg);
    top: 29px;
    left: 13px;
  }
  .site-header .header-content .container .logo {
    padding: 0;
    height: auto;
    margin: 0;
    float: none;
    margin-left: calc(((100vw - 40px)/ 2) - 75px);
  }
  .site-header .header-content .container .logo img {
    height: 30px;
    margin-right: 25px;
    margin-top: 10px;
  }
  .site-header .header-content .container .nav-toggle-menu {
    display: none;
  }
  .site-header .header-content .container .caret {
    display: none;
  }
  .site-header .header-content .container .account-container {
    margin-right: 5px;
    height: 50px;
    position: fixed;
    top: 0;
    right: 0;
    padding: 0;
    width: 85px;
  }
  .site-header .header-content .container .account-container .search-box {
    display: block;
    float: left;
  }
  .site-header .header-content .container .account-container .search-box .search-icon {
    color: #588d3f;
    font-size: 17px;
    margin: 16px 10px;
  }
  .site-header .header-content .container .account-container .user-menu {
    margin: 0;
    left: -135px;
    width: 187px;
  }
  .site-header .header-content .container .account-container .user-menu li a {
    padding: 10 15px;
  }
  .site-header .header-content .container .account-container .profile-no-login-icon {
    display: inline;
  }
  .site-header .header-content .container .account-container .profile-no-login-icon button {
    background: transparent;
    color: #588d3f;
    border: 0;
    padding: 14px;
    font-size: 17px;
  }
  .site-header .header-content .container .account-container .profile-no-login-icon ul {
    margin-top: 14px;
    left: -106px;
    border-top-right-radius: 0;
    padding-bottom: 10px;
  }
  .site-header .header-content .container .account-container .profile-no-login-icon ul a p {
    padding: 10px 0;
    margin-bottom: 0;
    font-size: 16px;
  }
  .site-header .header-content .container .account-container .auth-container {
    min-width: auto;
    padding: 7px 0;
    float: right;
  }
  .site-header .header-content .container .account-container .auth-container .user-name {
    display: none;
  }
  .site-header .header-content .container .account-container .auth-container .user-avatar-circle {
    width: 25px;
    height: 25px;
    margin-top: 5px;
    margin-right: 10px;
  }
  .site-header .header-content .container .account-container .register-box {
    display: none;
  }
  .site-header .header-content .container .account-container .login-box {
    display: none;
  }
  .main .main-user-left .item-user i {
    margin: 0;
  }
  .login-container {
    width: 70%;
  }
  .login-container .login-left-container {
    display: none;
  }
  .login-container .login-right-container {
    width: 100%;
    height: auto;
  }
  .footer {
    padding-bottom: 10px;
  }
  .footer .center-container {
    position: relative;
    width: 100%;
  }
  .footer .center-container ul li a {
    font-size: 14px;
    padding: 5px 0;
  }
  .footer .center-container .general-box .footer-logo {
    height: 50px;
    margin-top: 0;
    position: absolute;
    right: calc((50vw - 144px));
    text-align: center;
  }
  .footer .center-container .general-box .footer-logo ul li {
    width: 100%;
  }
  .footer .center-container .general-infomation-box {
    padding: 0;
    text-align: center;
    position: absolute;
    margin-top: 0px;
    width: calc(50vw - 5px);
    text-align: left;
    font-size: 6px;
    padding-left: calc((50vw - 140px)/2);
  }
  .footer .center-container .general-infomation-box .title {
    text-align: left;
    margin-top: 0;
    font-size: 13px;
  }
  .footer .center-container .general-infomation-box p {
    font-size: 12px;
    padding-left: 20px;
  }
  .footer .center-container .links {
    margin-top: 15px;
    padding-bottom: 15px;
    position: absolute;
    top: 44px;
    right: 0;
    width: calc(50vw - 20px);
    text-align: left;
    color: white;
  }
  .footer .center-container .contact-box {
    padding: 15px 20px;
    text-align: center;
    margin-top: 245px;
  }
  .footer .center-container .contact-box a > p {
    text-align: justify;
  }
  .footer .center-container .contact-box .title {
    text-align: center;
    margin-top: 20px;
  }
  .footer .center-container .contact-box .address-dungmori {
    margin-bottom: 0;
  }
  .footer .center-container .contact-box p {
    font-size: 14px;
    padding: 4px 0;
  }
  .footer .center-container .contact-box .footer-maps {
    width: 100%;
  }
  .footer .center-container .social-box h3 {
    text-align: center;
    font-size: 14px;
  }
  .footer .center-container .social-box .social {
    text-align: center;
  }
  .footer .center-container .social-box .social img {
    height: 55px;
  }
  .home-featured-popup {
    width: 100%;
    padding: 15px;
    margin: 0;
    min-height: auto;
  }
  .home-featured-popup a img {
    width: 100%;
  }
  .user-profile-popup .user-profile-container .cover-container {
    width: auto;
  }
  .user-profile-popup .user-profile-container #user-info-table .info-item-email {
    font-size: 11px;
  }
  .user-profile-popup {
    width: 90%;
    min-height: 75%;
    margin: 0;
  }
  .ctrlq.fb-button {
    width: 40px;
    height: 40px;
    bottom: 30px;
    right: 15px;
  }
  .go-top {
    left: 15px !important;
  }
  .list-comments .comment-item .comment-content .reply-container .child-comment-item {
    width: calc(100vw - 80px);
  }
  .list-comments .comment-item .comment-content .reply-container .child-comment-item .comment-content {
    width: calc(100% - 50px);
  }
  .list-comments .comment-item .comment-content .reply-container .child-comment-item .comment-content .child-name {
    width: 100%;
    overflow-wrap: break-word;
  }
  .list-comments .comment-item .comment-content .reply-container .child-comment-item .delete-comment {
    float: right;
    margin-top: -20px;
  }
  .list-comments .comment-item .comment-content .reply-container .reply-form {
    width: calc(100vw - 80px);
  }
  .list-comments .comment-item .comment-content .reply-container .reply-form .input-comment {
    width: calc(100% - 50px);
    float: right;
    margin-right: 0;
  }
  .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {
    float: right;
    margin-top: 10px;
  }
}

@media only screen and (max-width: 481px) {
  .login-container {
    width: 90%;
  }
  .login-container .login-left-container {
    display: none;
  }
  .login-container .login-right-container {
    width: 100%;
    height: auto;
  }
}

@media only screen and (max-width: 767px) {
  .block-slide-banner {
    height: calc(100vw / 45 * 18);
    background-size: cover;
  }
  .block-slide-banner .player-home-icon {
    width: 60px;
    height: 60px;
    margin: calc((100vw / 54 * 25)/ 2 - 40px) auto;
  }
  .block-slide-banner .player-home-icon img {
    width: 100%;
  }
  .block-slide-banner .player-home-icon:hover {
    opacity: 0.8;
  }
  .buy-course-fixed {
    right: 15px;
    bottom: 80px;
  }
  .buy-course-fixed .circle-btn {
    width: 40px;
    height: 40px;
    padding-top: 10px;
  }
  .buy-course-fixed .circle-btn i {
    font-size: 22px;
  }
  .site-main {
    margin-top: 50px;
  }
  .site-main .block-buy-kh {
    height: auto;
    padding-top: 0;
    background: url(../img/slider/nen.png) no-repeat;
    background-size: 100% calc((100vw)*1.6);
  }
  .site-main .block-buy-kh .pc-content {
    display: none;
  }
  .site-main .block-buy-kh .mobile-content {
    display: block;
    width: 100%;
    float: left;
    padding: 0 0 0 15px;
    height: calc((100vw - 26px)*1.47);
    overflow-y: hidden;
  }
  .site-main .block-buy-kh .mobile-content .course-item {
    position: relative;
    width: calc(50% - 15px);
    float: left;
    margin-right: 15px;
    margin-bottom: 15px;
    box-sizing: border-box;
    color: #fff;
    height: calc((50vw - 26px)*1.47);
    float: left;
    background-image: url(../img/course-card.png);
    background-size: 100% calc((50vw - 22px)*1.85);
    border: 2px solid;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
  }
  .site-main .block-buy-kh .mobile-content .course-item .course-card {
    margin-top: calc((50vw - 27px)*0.23);
  }
  .site-main .block-buy-kh .mobile-content .course-item .course-card-combo {
    margin-top: calc((50vw - 27px)*0.16);
  }
  .site-main .block-buy-kh .mobile-content .course-item .course-name {
    font-size: 18px;
    font-weight: normal;
    margin-top: 5px;
    text-shadow: -1px 0 #018930, 0 1px #018930, 1px 0 #018930, 0 -1px #018930;
  }
  .site-main .block-buy-kh .mobile-content .course-item .course-name .course-name-main {
    font-size: 18px;
  }
  .site-main .block-buy-kh .mobile-content .course-item .course-info {
    float: none;
    font-size: 12px;
    color: #fff;
  }
  .site-main .block-buy-kh .mobile-content .course-item .buy-info {
    float: none;
  }
  .site-main .block-buy-kh .mobile-content .course-item .buy-info .buy-btn {
    position: absolute;
    bottom: 10px;
    color: #fff;
    background-color: #66a532;
    width: calc((50vw - 44px));
    padding: 6px 12px;
    border-radius: 20px;
    margin-left: 9px;
  }
  .site-main .block-buy-kh .view-more-courses {
    display: block;
    width: calc(100% - 30px);
    float: left;
    margin-left: 15px;
    text-align: center;
  }
  .site-main .block-buy-kh .center-container .buy-course-title {
    margin-bottom: 25px;
  }
  .site-main .block-buy-kh .center-container .buy-course-title span {
    font-size: 15px;
    line-height: normal;
  }
  .site-main .block-buy-kh .center-container .slick-next {
    display: none !important;
  }
  .site-main .nav-tabs {
    margin-top: 10px;
  }
  .site-main .nav-tabs .tab-item {
    margin-right: 10px;
  }
  .site-main .nav-tabs .tab-item a {
    font-size: 18px;
  }
  .site-main .block-all-courses .center-container .see-more-course {
    font-weight: normal;
  }
  .site-main .block-all-courses .center-container .from-us-span {
    display: none;
  }
  .site-main .block-all-courses .center-container .course-header {
    font-size: 24px;
    margin-bottom: 0px;
    padding: 30px 8px 0px 10px;
  }
  .site-main .block-all-courses .center-container .preview-course-container {
    width: 100%;
    float: left;
    padding: 0 15px;
  }
  .site-main .block-all-courses .center-container .preview-course-container .grid-item-course {
    margin-top: 10px;
    height: auto;
    border: 0.5px solid #82ae1d;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important;
  }
  .site-main .block-all-courses .center-container .preview-course-container .grid-item-course .course-detail a b {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
  }
  .site-main .block-all-courses .center-container .preview-course-container .head-title {
    font-size: 16px;
    margin-top: 40px;
  }
  .site-main .block-all-courses .center-container .preview-course-container .head-title .head-title-left {
    float: left;
    color: white;
    background-color: #82ae1d;
    font-size: 15px;
    padding: 6px 14px;
    border-radius: 15px;
  }
  .site-main .block-all-courses .center-container .preview-course-container .head-title .see-more {
    margin-top: 4px;
    font-style: italic;
  }
  .site-main .block-all-courses .center-container .preview-course-container .head-title .zmdi-more {
    display: none;
  }
  .site-main .block-all-courses .center-container .preview-course-container .courses-linear {
    margin-top: 5px;
  }
  .site-main .block-all-courses .center-container .preview-course-container .courses-linear .grid-item-course:last-child {
    display: none;
  }
  .site-main .block-all-courses .center-container .preview-course-container .courses-linear .course-detail {
    height: 50px;
  }
  .site-main .block-all-courses .center-container .preview-course-container .courses-linear .course-detail .arthor {
    padding: 2px 5px 5px 5px;
    width: calc((100vw)/2 - 55px);
  }
  .site-main .block-all-courses .center-container .preview-course-container .courses-linear .course-detail img {
    width: 16px;
    height: 16px;
    padding-top: 4px;
    padding-left: 5px;
    float: left;
  }
  .site-main .block-all-courses .center-container .preview-course-container .courses-linear .grid-item-course {
    width: calc((100vw)/2 - 22px);
    float: left;
  }
  .site-main .block-all-courses .center-container .preview-course-container .courses-linear .grid-item-course .course-thumbnail {
    height: calc(((100vw)/2 - 22px)*0.62);
  }
  .site-main .block-all-courses .center-container .preview-course-container .courses-linear .grid-item-course .course-detail .name {
    padding: 8px 5px 3px 5px;
    font-size: 13px;
  }
  .site-main .block-student-feedback {
    min-height: 500px;
  }
}

@media only screen and (max-width: 767px) and (max-width: 767px) {
  .site-main .block-student-feedback .center-container {
    background: url(../img/nenhocvien-mobile.png) no-repeat;
    background-size: 100%;
  }
}

@media only screen and (max-width: 767px) {
  .site-main .block-student-feedback .center-container .img-student {
    display: block;
  }
  .site-main .block-student-feedback .student-feedback-title {
    padding: 10px 15px;
  }
  .site-main .block-student-feedback .student-feedback-title h1 {
    font-size: 22px;
  }
  .site-main .block-student-feedback .student-feedback-title h2 {
    font-size: 22px;
    margin-top: 10px;
  }
  .site-main .block-student-feedback .student-feedback-title h2 span {
    font-size: 34px;
  }
  .site-main .block-student-feedback .student-feedback-container {
    padding: 30px 15px 10px 15px;
  }
  .site-main .block-student-feedback .student-feedback-container .slick-next {
    display: none !important;
  }
  .site-main .block-student-feedback .student-feedback-container .slick-prev {
    display: none !important;
  }
  .site-main .block-student-feedback .student-feedback-container .student-feedback-slider .slick-list .slick-track .student-feedback-item {
    margin: 0 7px;
    text-align: justify;
  }
  .site-main .block-student-feedback .student-feedback-container .student-feedback-slider .slick-list .slick-track .student-feedback-item .student-avatar {
    width: 45px;
    height: 45px;
    margin-left: calc(((100vw - 42px)/3 - 55px)/2);
  }
  .site-main .block-student-feedback .student-feedback-container .student-feedback-slider .slick-list .slick-track .student-feedback-item .student-info-box {
    margin: 0;
  }
  .site-main .block-student-feedback .student-feedback-container .student-feedback-slider .slick-list .slick-track .student-feedback-item .student-info-box .student-info {
    padding: 0;
    text-align: center;
  }
  .site-main .block-student-feedback .student-feedback-container .student-feedback-slider .slick-list .slick-track .student-feedback-item .student-info-box .student-info-avatar {
    padding: 0;
    text-align: center;
  }
  .site-main .block-student-feedback .student-feedback-container .student-feedback-slider .slick-list .slick-track .student-feedback-item .student-info-box .student-info-avatar img {
    width: 80px;
    height: 80px;
    display: inline;
    margin-left: 0;
  }
  .site-main .block-student-feedback .student-feedback-container .student-feedback-slider .slick-list .slick-track .student-feedback-item .student-info-box .student-name {
    font-size: 18px;
    margin-top: 20px;
  }
  .site-main .block-student-feedback .student-feedback-container .student-feedback-slider .slick-list .slick-track .student-feedback-item .student-info-box .student-name {
    margin-top: 10px;
  }
  .site-main .block-payment-info .payment-ctn {
    background: url("../img/nen_vn_jp.png") no-repeat;
    background-size: 100%;
  }
  .site-main .block-payment-info .payment-header {
    margin: 14px 7px;
    font-size: 16px;
  }
  .site-main .block-payment-info .payment-ctn .payment-jp-left {
    padding-left: 15px;
    width: 100%;
    text-align: left;
  }
  .site-main .block-payment-info .payment-ctn .payment-vn-right {
    padding-left: 15px;
    width: 100%;
    text-align: left;
  }
  .site-main .block-payment-info .payment-ctn h3 {
    margin-top: 7px;
    font-size: 15px;
  }
  .site-main .block-payment-info .payment-ctn p {
    margin-top: 10px;
    padding-left: 20px;
    font-size: 12px;
  }
  .site-main .fb-comments-container {
    padding: 0 15px;
  }
  .site-main .fb-comments-container .col-md-4 {
    padding: 0;
  }
  .site-main .fb-comments-container .col-md-4 iframe {
    width: 100% !important;
    height: 250px !important;
  }
  .site-main .fb-comments-container .center-container .row-content-fb {
    margin: 0;
  }
  .site-main .fb-comments-container .center-container .row-content-fb .col-content {
    padding: 0;
  }
}

@media only screen and (max-width: 767px) {
  .main .search-key-title {
    padding: 25px 15px 10px 15px;
    margin: 0;
    font-size: 25px;
  }
  .main .all-search-result-container {
    width: 100%;
    padding: 0 15px;
  }
  .main .all-search-result-container .search-result-item .infor-box {
    width: calc(100% - 100px);
    padding-left: 10px;
    padding-right: 0;
  }
  .main .all-search-result-container .search-result-item .infor-box .title-search-item {
    font-size: 16px;
  }
  .main .all-search-result-container .search-result-item .infor-box .content-search-item {
    width: 100%;
  }
  .main .paginate-container {
    width: auto;
    padding: 0 10px;
  }
}

@media only screen and (max-width: 767px) {
  .main {
    min-height: auto;
  }
  .main .main-center .main-title {
    margin-top: 20px;
    text-align: center;
  }
  .main .main-center .teacher-slider {
    padding: 20px 0;
  }
  .main .main-center .teacher-text-intro {
    font-size: 19px;
    padding: 10px 30px 20px 30px;
  }
  .main .main-center .slick-arrow {
    display: none !important;
  }
  .teacher-slider .teacher-slider-item {
    height: auto;
  }
  .teacher-slider .teacher-slider-item img {
    height: auto;
  }
  .block-teacher {
    padding-bottom: 0;
  }
  .block-teacher .center-container {
    width: 100%;
    min-height: auto;
    padding-left: 15px;
    padding-right: 15px;
  }
  .block-teacher .center-container .teacher-image {
    width: 150px;
    height: 150px;
  }
  .block-teacher .center-container .teacher-image-content {
    width: 150px;
    height: 150px;
  }
  .block-teacher .center-container .teacher-image-content img {
    width: 90px;
    margin-left: 32px !important;
    margin-top: 14px;
    max-width: 90px;
  }
  .block-teacher .center-container .teacher-info {
    width: calc(100vw - 180px);
    padding-left: 5px;
    padding-top: 0;
    min-height: 185px;
  }
  .block-teacher .center-container .teacher-info h2 {
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 20px;
  }
  .block-teacher .center-container .teacher-info h3 {
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 15px;
    text-align: justify;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 5;
  }
  .block-teacher .center-container .teacher-info .read-more {
    margin-top: 3px;
    border-radius: 25px;
  }
  .block-teacher-detail {
    padding: 0 15px;
  }
  .block-teacher-detail .center-container .teacher-info {
    width: 100%;
    height: auto;
    padding: 55px 0 0 0;
    text-align: center;
  }
  .block-teacher-detail .center-container .teacher-info h2 {
    font-size: 27px;
    margin-top: 20px;
  }
  .block-teacher-detail .center-container .teacher-info h4 {
    font-size: 15px;
  }
  .block-teacher-detail .center-container .teacher-info h4 i {
    font-size: 12px;
  }
  .block-teacher-detail .center-container .teacher-image {
    width: 80%;
    height: calc(100vw * 0.8 * 421 / 242);
    padding-top: 10px;
    margin-left: 10%;
  }
  .block-teacher-detail2 .center-container {
    padding: 30px 0;
  }
  .block-teacher-detail2 .center-container .teacher-intro-container {
    position: relative;
    width: 100%;
    height: auto;
    margin-bottom: 50px;
  }
  .block-teacher-detail2 .center-container .teacher-intro-container img {
    width: calc(100vw);
    height: calc(100vw * 9 / 16);
  }
  .block-teacher-detail2 .center-container .teacher-intro-container .play-icon-btn {
    position: absolute;
    margin: 0;
    top: calc(50% - 36px);
    left: calc(50% - 36px);
  }
  .block-teacher-detail2 .center-container .teacher-block-info {
    width: 100%;
    padding: 0 15px;
    margin: 0;
  }
  .block-teacher-detail3 {
    padding: 30px 15px 30px 15px;
  }
  .block-teacher-detail3 .center-container .head-title {
    font-size: 17px;
    margin-top: 0;
  }
  .block-teacher-detail3 .center-container .preview-course-container .courses-linear .grid-item-course {
    width: calc((100vw - 46px) / 2);
    height: auto;
    margin-right: 15px;
    border: 1px solid #eee;
  }
  .block-teacher-detail3 .center-container .preview-course-container .courses-linear .grid-item-course .course-detail .name {
    padding: 8px 8px 5px 6px;
  }
  .block-teacher-detail3 .center-container .preview-course-container .courses-linear .grid-item-course .course-detail .arthor {
    padding: 0 5px 7px 5px;
    width: calc(100% - 23px);
  }
  .block-teacher-detail3 .center-container .preview-course-container .courses-linear .grid-item-course .course-detail img {
    float: left;
    width: 16px;
    height: 16px;
    margin-left: 5px;
    margin-top: 0;
  }
  .block-teacher-detail3 .center-container .preview-course-container .courses-linear .grid-item-course .course-thumbnail {
    height: calc((50vw - 47px)*0.75);
  }
  .block-teacher-detail3 .center-container .preview-course-container .courses-linear .grid-item-course:last-child {
    display: none;
  }
  .block-teacher-detail3 .center-container .preview-course-container .courses-linear {
    padding-bottom: 0;
    width: calc(100% + 15px);
  }
  .full-container .center-container {
    width: 100%;
  }
  .full-container .center-container .comment-container {
    width: calc(100vw);
  }
  .full-container .center-container .comment-container ul li {
    width: calc((100vw)/2);
  }
  .full-container .center-container .comment-container ul li a {
    font-size: 13px;
  }
  .full-container .center-container .tab-content #user-comment-content .list-comments {
    padding: 15px;
  }
  .full-container .center-container .tab-content #user-comment-content .list-comments .input-comment-container .input-comment {
    width: calc(100vw - 150px);
    margin: 0px 5px;
    padding: 8px 8px;
  }
  .full-container .center-container .tab-content #user-comment-content .list-comments .end-of-list {
    width: calc(100vw - 30px);
  }
  .full-container .center-container .tab-content #user-comment-content .list-comments .comment-item .comment-content .name {
    width: calc(100%);
  }
}

@media only screen and (max-width: 767px) {
  .break-line {
    display: none;
  }
  .main .main-center .main-support-left {
    width: 100%;
    padding: 30px 15px 15px 15px;
    min-height: auto;
  }
  .main .main-center .main-support-left .item-question-heading {
    color: green;
    margin-bottom: 5px;
    font-weight: bold;
  }
  .main .main-center .main-support-left .item-question {
    width: 100%;
    padding: 10px 0;
    color: black;
    line-height: 24px;
    margin: 0;
    font-size: 15px;
    border-bottom: 1px solid #ced0d4;
  }
  .main .main-center .main-support-left .item-question:first-child {
    border-top: 1px solid #ced0d4;
  }
  .main .main-center .main-support-left .active {
    color: green;
  }
  .main .main-support-right {
    width: 100%;
    padding: 10px 15px 0 15px;
    border-left: none;
  }
  .main .main-support-right .block-hotro .block-content {
    margin: 0;
    padding-top: 10px;
  }
  .main .main-support-right .block-hotro .block-content .support-right {
    padding: 0;
    margin-top: 40px;
  }
  .main .main-support-right .block-contact {
    padding: 0;
  }
  .main .main-support-right .support-detail-container .support-detail-title {
    margin-top: 5px;
    font-size: 23px;
  }
  .main .main-support-right .support-detail-container .support-detail-content .main-content {
    padding-bottom: 10px;
    padding-top: 0px;
    text-align: justify;
  }
  .main .main-support-right .support-detail-container .support-detail-content .main-content p {
    text-align: justify;
  }
  .main .main-support-right .support-detail-container {
    width: 100%;
  }
  .main .main-support-right .support-detail-container .comment-container .comment-heading span {
    font-size: 21px;
  }
  .main .main-support-right .support-detail-container .support-detail-info {
    width: 100%;
  }
  .main .loader-area .loader {
    margin-top: calc((100vh - 100px)/2);
    border: 2px solid white;
    border-top: 2px solid #3498db;
    width: 50px;
    height: 50px;
  }
}

@media only screen and (max-width: 767px) {
  .main .main-center .main-left {
    width: 100%;
  }
  .main .main-center .main-left .heading-box {
    padding: 0 15px;
  }
  .main .main-center .main-left .heading-box .selectpicker {
    border: 1px solid gray;
    background-color: white;
    padding: 5px;
    border-radius: 4px;
  }
  .main .main-center .main-left .heading-box .blog-heading {
    text-align: center;
    margin-top: 20px;
    margin-bottom: 20px;
    font-size: 23px;
  }
  .main .main-center .main-left .heading-box .selectpicker {
    display: inline;
  }
  .main .main-center .main-left .heading-box .prefix-title {
    display: inline;
  }
  .main .main-center .main-left .featured {
    width: 100%;
    padding: 10px 15px;
  }
  .main .main-center .main-left .featured a .title {
    width: 100%;
    font-size: 19px;
    padding: 0;
  }
  .main .main-center .main-left .featured a .lazyload {
    width: 100%;
    height: calc(100vw * 9 / 16);
  }
  .main .main-center .main-left .featured .info {
    display: none;
  }
  .main .main-center .main-left .featured .brief {
    width: 100%;
    padding: 0;
  }
  .main .main-center .main-left .featured-sub {
    margin-top: 20px;
    width: 100%;
    float: left;
    padding: 0 15px;
    margin-left: 0;
  }
  .main .main-center .main-left .featured-sub a .title {
    width: calc(100vw - 165px);
    font-size: 16px;
    padding: 0;
    margin-left: 15px;
  }
  .main .main-center .main-left .featured-sub a .lazyload {
    width: 120px;
    height: 80px;
  }
  .main .main-center .main-left .bgbr {
    display: none;
  }
  .main .main-center .main-left .featured-more {
    padding: 15px 15px;
  }
  .main .main-center .main-left .featured-more a .lazyload {
    width: 120px;
    height: 80px;
  }
  .main .main-center .main-left .featured-more a .title {
    width: calc(100vw - 165px);
    font-size: 16px;
    padding: 0;
    margin-left: 15px;
    margin-top: 10px;
    color: #555;
  }
  .main .main-center .main-left .featured-more .info {
    display: none;
  }
  .main .main-center .main-left .featured-more .brief {
    display: none;
  }
  .main .main-center .main-left .featured-more .read-more {
    display: none;
  }
  .main .main-center .main-left .pagination {
    padding: 0 15px;
    margin: 10px 0;
  }
  .main .main-center .main-left .blog-detail-container {
    width: 100%;
    margin: 0;
    padding: 0 15px;
  }
  .main .main-center .main-left .blog-detail-container .blog-detail-thumb {
    width: 100vw;
    max-width: 100vw;
    margin-left: -15px;
    height: auto;
  }
  .main .main-center .main-left .blog-detail-container .blog-detail-title {
    margin-top: 20px;
    margin-bottom: 20px;
    font-size: 23px;
    padding-left: 0 15px;
  }
  .main .main-center .main-left .blog-detail-container .blog-detail-info {
    width: 100%;
  }
  .main .main-center .main-left .blog-detail-container .blog-detail-content .main-content {
    text-align: justify;
    word-wrap: break-word;
  }
  .main .main-center .main-left .blog-detail-container .comment-container .comment-heading span {
    font-size: 21px;
  }
  .main .main-center .main-left .blog-detail-container .comment-container .fb-comments {
    margin-left: 0px;
  }
  .main .main-center .main-right {
    width: 100%;
    padding: 0 15px;
    margin: 0;
  }
  .main .main-center .main-right .list-category {
    display: none;
  }
  .main .main-center .main-right .related-news-item {
    padding: 10px 0;
  }
  .main .main-center .main-right .related-news-item a .lazyload {
    width: 102px;
    height: 68px;
  }
  .main .main-center .main-right .related-news-item a .title {
    margin-left: 10px;
    width: calc(100vw - 160px);
  }
  .main .main-center .main-right .related-news-item a .info {
    margin-left: 10px;
    width: calc(100vw - 160px);
  }
}

@media only screen and (max-width: 767px) {
  .main .main-course .main-left {
    padding: 0 15px;
  }
  .main .main-course .main-left #myModal .test-dialog {
    width: auto;
  }
  .main .main-course .main-left #myModal .test-dialog .content-test-detail {
    max-width: calc(100vw - 50px) !important;
  }
  .main .main-course .main-left .course-detail-title {
    margin: 20px 0 20px 0;
    font-size: 20px;
  }
  .main .main-course .main-left .course-heading span {
    font-size: 18px;
  }
  .main .main-course .main-left .cover-container .movie-play img {
    margin-left: -15px;
    max-width: 100vw;
    height: calc((100vw)/1.77);
  }
  .main .main-course .main-left .cover-container .movie-play .play-icon-btn {
    margin-top: calc((100vw)/(-3.54) - 40px);
    margin-left: calc((100vw - 102px)/2);
    padding-top: 2px;
  }
  .main .main-course .main-left .cover-container iframe {
    width: calc(100vw) !important;
    height: calc(100vw * 9 / 16) !important;
    margin-left: -15px;
  }
  .main .main-course .main-left .cover-container .myplayer {
    width: calc(100vw) !important;
    height: calc(100vw * 9.5 / 16) !important;
    margin-left: -15px;
  }
  .main .main-course .main-left .guest-cover-container {
    padding: 50px 0;
  }
  .main .main-course .main-left .guest-cover-container h3 {
    font-size: 18px;
  }
  .main .main-course .main-left .course-tab li a {
    font-size: 14px;
    text-align: center;
    padding: 5px 0px 5px 0px;
  }
  .main .main-course .main-left .course-tab li {
    text-indent: 0;
  }
  .main .main-course .main-left .comment-container .comment-tab .user-tab {
    text-align: center;
    width: calc((100vw - 47px) * 0.38);
  }
  .main .main-course .main-left .comment-container .comment-tab .user-tab a {
    font-size: 14px;
    padding-left: 0;
    padding-right: 0;
  }
  .main .main-course .main-left .comment-container .comment-tab .facebook-tab {
    text-align: center;
    width: calc((100vw - 47px) * 0.62);
  }
  .main .main-course .main-left .comment-container .comment-tab .facebook-tab a {
    font-size: 14px;
    padding-left: 0;
    padding-right: 0;
  }
  .main .main-course .main-left .course-detail-container {
    text-align: justify;
  }
  .main .main-course .main-left .comment-container .fb-comments {
    margin: 0;
  }
  .main .main-course .main-left #user-comment-content .list-comments .comment-item .comment-content .name {
    width: 100%;
    text-align: justify;
    word-wrap: break-word;
  }
  .main .main-course .main-left .cover-container .server-localtion-container {
    text-align: center;
  }
  .main .main-course .main-left .cover-container .server-localtion-container .server-item {
    display: none;
  }
  .main .main-course .main-left .cover-container .server-localtion-container .server-item .server-localtion {
    width: 70%;
    padding: 10px 15px;
  }
  .main .main-course .main-left .cover-container .server-localtion-container .select-localtion-select {
    display: block;
    opacity: 1;
    color: black;
    float: left;
    padding: 6px 7px !important;
    border: 1px solid #588d3f;
    outline: none;
    background: #588d3f;
    font-size: 12px;
  }
  .main .main-course .main-left .cover-container .server-localtion-container .choose-quality {
    width: 100%;
    font-size: 12px;
  }
  .main .main-course .main-left .cover-container .server-localtion-container .choose-quality .select-video-quality {
    opacity: 1;
    color: black;
    float: right;
    padding: 6px 7px !important;
    border: 1px solid #e74c3c;
    outline: none;
  }
  .main .main-course .main-left .cover-container .server-localtion-container .choose-quality .select-video-quality option {
    color: black;
  }
  .main .main-course .main-left .cover-container {
    min-height: auto;
    margin: 10px 0 60px 0;
  }
  .main .main-course .main-left .cover-container .lesson-content-detail .review-result {
    position: static;
    margin-top: 10px;
    float: none;
  }
  .main .main-course .main-left .cover-container .lesson-content-detail .remove-result {
    position: static;
    margin-top: 10px;
  }
  .main .main-course .main-left .preview-course-container {
    display: block;
    padding-bottom: 25px;
  }
  .main .main-course .main-left .preview-course-container .course-item {
    width: 100%;
    float: left;
    margin-bottom: 20px;
  }
  .main .main-course .main-left .preview-course-container .course-item img {
    height: 163px;
    width: 100%;
    float: left;
    -o-object-fit: cover;
    object-fit: cover;
  }
  .main .main-course .main-left .combo-list-container .combo-item .combo-name-container {
    padding: 30px 15px;
    width: 100px;
  }
  .main .main-course .main-left .combo-list-container .combo-item .combo-detail-container {
    width: calc(100% - 100px);
  }
  .main .main-course .main-left .combo-list-container .combo-item .combo-detail-container .course-info {
    width: calc(100% - 15px);
    font-size: 14px;
    margin-top: 0;
  }
  .main .main-course .main-left .combo-list-container .combo-item .combo-detail-container .dmr-btn {
    padding: 5px;
    width: 90px;
    font-size: 13px;
    margin-top: 10px;
  }
  .main .main-course .main-right {
    padding-bottom: 25px;
  }
  .main .main-course .course-info-container {
    margin-top: 0 !important;
  }
  .main .main-center .all-courses-title {
    text-align: center;
    padding: 0 15px;
    margin: 20px 0 20px 0;
    font-size: 22px;
  }
  .main .main-center .all-courses-container:last-child .course-item:last-child {
    margin-left: calc(25vw - 5px);
  }
  .main .main-center .all-courses-container {
    padding: 0 15px;
    margin: 0;
  }
  .main .main-center .all-courses-container .course-item {
    width: calc((100vw - 45px)/2);
    height: 270px;
    margin-bottom: 20px;
  }
  .main .main-center .all-courses-container .course-item .course-info {
    padding-left: 10px;
    padding-right: 10px;
  }
  .main .main-center .all-courses-container .course-item .course-card .course-name {
    margin-bottom: 10px;
  }
  .main .main-center .all-courses-container .course-item .course-card .course-info {
    font-size: 12px;
  }
  .main .main-center .all-courses-container .course-item .combox4 .course-name {
    font-size: 19px;
    margin-bottom: 26px;
    margin-top: 27px;
  }
  .main .main-center .all-courses-container .combo-it .combo .course-name {
    font-size: 35px;
    margin-top: 15px;
  }
  .main .main-center .all-courses-container .combo-x3 .combox3 .course-name {
    font-size: 25px;
    margin-top: 24px;
    margin-bottom: 21px;
  }
}

@media only screen and (max-width: 767px) {
  .main .main-page-center {
    padding: 0 15px;
  }
  .main .main-page-center .page-detail-container {
    width: 100%;
  }
  .main .main-page-center .page-detail-container .page-detail-title {
    margin: 20px 0 10px 0;
  }
  .main .main-page-center .page-detail-container .page-detail-info {
    width: 100%;
  }
  .main .main-page-center .page-detail-container .page-detail-content .main-content {
    text-align: justify;
  }
  .main .main-page-center .page-detail-container .page-detail-content .main-content p {
    text-align: justify;
  }
  .main .main-page-center .page-detail-container .page-detail-content .main-content span {
    text-align: justify;
  }
  .main .main-page-center .page-detail-container .comment-container .comment-heading span {
    font-size: 21px;
  }
  .main .main-page-center .page-detail-container .comment-container .fb-comments {
    margin: 0;
  }
}

@media only screen and (max-width: 767px) {
  .main .main-payment .nav-wizard {
    margin-top: 0;
    z-index: 998;
  }
  .main .main-payment .nav-wizard .step-text {
    display: none;
  }
  .main .main-payment .nav-wizard li a {
    border-radius: 0 !important;
  }
  .main .main-payment .mb-stt {
    padding: 5px 15px;
    display: block;
  }
  .main .main-payment .mb-stt .step-mb {
    padding: 0 4px;
    border-radius: 50%;
    background-color: #588d3f;
    color: #fff;
  }
  .main .main-payment .steps-container {
    margin-top: 0px;
  }
  .main .main-payment .steps-container .steps-container-left {
    width: 100%;
    padding-bottom: 30px;
  }
  .main .main-payment .steps-container .steps-container-left input {
    border: 1px solid #DDD;
  }
  .main .main-payment .steps-container .steps-container-left .table {
    width: 100vw;
    margin-left: -15px;
  }
  .main .main-payment .steps-container .steps-container-left .guest-info-container {
    padding: 50px 0;
  }
  .main .main-payment .steps-container .steps-container-left .customer-info-container .customer-info-table {
    padding: 5px 15px 0px 15px;
  }
  .main .main-payment .steps-container .steps-container-left .customer-info-container .customer-info-table .user-form-item {
    width: auto;
  }
  .main .main-payment .steps-container .steps-container-left .customer-info-container .customer-successfull-gates {
    padding: 20px 15px 20px 15px;
    text-align: justify;
  }
  .main .main-payment .steps-container .steps-container-left .invoice-warning .close {
    margin: -13px -9px 2px 5px;
  }
  .main .main-payment .steps-container .steps-container-left .step-1-container .customer-info-container {
    padding-bottom: 10px;
  }
  .main .main-payment .steps-container .steps-container-left .step-1-container .customer-info-container .payment-heading span {
    margin-left: 0;
    padding-left: 15px;
    font-size: 19px;
  }
  .main .main-payment .steps-container .steps-container-left .step-1-container .customer-info-container .payment-heading .refresh {
    padding-right: 15px;
    font-size: 17px;
  }
  .main .main-payment .steps-container .steps-container-left .step-2-container .customer-choose-payment-gates label span {
    width: calc(100% - 35px);
  }
  .main .main-payment .steps-container .steps-container-left .continue-container .dmr-btn {
    float: left;
    margin-left: calc((100vw - 167px)/2);
  }
  .main .main-payment .steps-container .steps-container-left .continue-container .buy-btn {
    padding: 10px 15px;
    font-size: 16px;
    margin-right: 15px;
  }
  .main .main-payment .steps-container .steps-container-left .continue-container .light-btn {
    padding: 10px 15px;
    font-size: 16px;
  }
  .main .main-payment .steps-container .steps-container-left .invoice-warning .label {
    width: 100%;
    margin-bottom: 7px;
    font-size: 14px;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .main .main-payment .steps-container .steps-container-left .delivery-info {
    width: calc(100vw);
    margin-left: -25px;
  }
  .main .main-payment .steps-container .steps-container-left .customer-info-container {
    padding-bottom: 0;
  }
  .main .main-payment .steps-container .steps-container-left .customer-choose-payment-gates {
    padding-bottom: 0 !important;
  }
  .main .main-payment .steps-container .steps-container-left .link-checkout {
    word-wrap: break-word !important;
  }
  .main .main-payment .steps-container .steps-container-right {
    margin-bottom: 20px;
    margin-left: calc((100vw - 355px)/2);
    padding: 0;
  }
  .main .main-payment .steps-container .steps-container-right .payment-info-container .combo-item {
    padding: 10px 10px 15px 10px;
  }
  .main .main-payment .steps-container .steps-container-right .payment-info-container .combo-item .combo-detail-container .course-info {
    padding-left: 15px;
  }
  .main .main-payment .steps-container .steps-container-left .continue-container {
    padding-left: 15px;
    padding-right: calc((100vw - 332px)/2);
  }
  .main .main-payment .checkout-container {
    width: 100%;
    padding: 0 15px;
  }
}

@media only screen and (max-width: 481px) {
  .main .main-center .steps-container .steps-container-right {
    margin-left: 0;
    width: calc(100vw - 15px);
    padding-left: 15px;
  }
  .main .main-payment .steps-container .steps-container-left .continue-container {
    padding-right: calc((100vw - 322px)/2);
  }
  .main .main-payment .steps-container .steps-container-left .continue-container .light-btn {
    margin-right: 5px;
  }
}

/*# sourceMappingURL=mobile_styles.css.map */
