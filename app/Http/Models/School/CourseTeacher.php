<?php

namespace App\Http\Models\School;

use App\Http\Models\Community\CommunityGroup;
use App\Http\Models\Community\CommunityGroupCross;
use Illuminate\Database\Eloquent\Model;
use DB;

class CourseTeacher extends Model
{
    protected $connection = 'mysql3';

    protected $table_name = 'course_teachers_tbl';

    protected $fillable = ['course_id', 'group_id', 'teacher_id'];

    public $timestamps = FALSE;

    public function __construct() {
      $this->table = DB::connection($this->connection)->getDatabaseName() . '.' . $this->table_name;
    }

    public function group()
    {
      return $this->hasOne(CommunityGroupCross::class, 'id', 'group_id');
    }

    public function teacher()
    {
      return $this->belongsTo(SchoolUser::class, 'teacher_id');
    }
}
