var myclient = new ClientJS();
var userIp = '';

// $.getJSON('https://jsonip.com/?callback=?', function(response) {
//   // console.log("thông tin người dùng ipify: ",response);
//   userIp = response.ip;
// });

var user = new Vue({
  el: '#register-form',
  data: {

    buttonState : true,
    error : null
  },
  methods: {

    /* đăng ký */
    register: function(){

        var vm = this;
        vm.buttonState = false;
        var currentBrowser = myclient.getBrowser();
        var currentOsName = myclient.getOS();
        var currentFingerprint = myclient.getFingerprint();
        var registerForm = $("#register-form");
        var formData = registerForm.serialize();
        formData += '&browser=' + currentBrowser + '&os=' + currentOsName + '&fingerprint=' + currentFingerprint + '&userIp=' + userIp;

        console.log(formData);
      
        setTimeout(function(){
            $.ajax({
                url:window.location.origin+'/register', type:'POST', data:formData, 
                error: function (errors) {

                    // console.log(errors.responseJSON);
                    // $('#error-register-content').css('color', '#a94442');
                    vm.buttonState = true;

                    if(errors.responseJSON.errors != null)
                        vm.error = errors.responseJSON.errors;
                },
                success:function(response){
                    vm.buttonState = true;
                    // $('#error-register-content').css('color', '#588d3f');
                    // vm.error = "Bạn đã tạo tài khoản thành công. Vui lòng truy cập email của bạn để xác thực tài khoản bạn vừa tạo";
                    // console.log(response);
                    location.reload();
                }
            });
        }, 1000);
    },

    /* ẩn thông báo lỗi */
    hideError: function(){
      this.error = null;
    }

  },
  mounted: function() {}
});