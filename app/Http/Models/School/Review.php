<?php

namespace App\Http\Models\School;

use App\Http\Models\Admin;
use App\Http\Models\Community\CommunityGroup;
use Illuminate\Database\Eloquent\Model;

class Review extends Model
{
    protected $connection = 'mysql3';

    protected $table = 'reviews';

    protected $fillable = [
      'batch_id', 'user_id', 'created_by',
      'crit_1', 'crit_2', 'crit_3', 'crit_4', 'crit_5', 'crit_6', 'crit_7', 'crit_8', 'crit_9', 'crit_10', 'crit_11',
      'crit_avg', 'rank', 'title', 'note'
    ];

    public function user()
    {
      return $this->belongsTo(SchoolUser::class, 'user_id');
    }

    public function group()
    {
      return $this->belongsTo(CommunityGroup::class, 'group_id');
    }

    public function reviewer()
    {
      return $this->belongsTo(Admin::class, 'created_by');
    }
}
