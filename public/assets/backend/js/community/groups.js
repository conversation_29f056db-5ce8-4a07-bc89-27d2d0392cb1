"use strict";$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}});var router=new VueRouter({mode:"history"});Vue.filter("dateTimeToMinute",function(t){return moment(t).format("HH:mm DD/MM/YYYY")});var exam__results=new Vue({el:"#group__screen",components:{paginate:VuejsPaginate},data:function(){return{url:window.location.origin,loading:!1,items:[],filter:{id:void 0,sort:"",page:1,per_page:20,total_page:10},total_result:0,currentModal:null,currentGroup:null,modal_titles:{communityGroupMembers:"Danh sách thành viên",communityGroupPosts:"Danh sách bài đăng",communityGroupHashtags:"Danh sách hashtag"}}},methods:{changePage:function(t){var e=this;e.filter.page=t;var n=_.omit(_.pickBy(e.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page"]);e.getList(n),router.replace(window.location.pathname+"?"+$.param(n))},applyFilter:function(){var t=this;t.filter.page=1;var e=_.omit(_.pickBy(t.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page","page"]);t.getList(e),router.replace(window.location.pathname+"?"+$.param(e))},onChangeCheckbox:function(t,e){vm=this,vm.filter[t]=e.target.checked?1:0,vm.applyFilter()},getList:function(t){var e=this;e.loading=!0,setTimeout(function(){$.post(window.location.origin+"/backend/community/group/list",t,function(t){200===t.code?(e.items=t.data.groups.map(function(t){return t}),e.filter.total_page=t.data.pagination.last_page,e.total_result=t.data.pagination.total):alert("Có lỗi! Liên hệ dev!!!"),e.loading=!1})},200)},setFilterByUrl:function(){var t=this,e=$.deparam.querystring();_.forEach(e,function(e,n){t.filter[n]=e==parseInt(e)?parseInt(e):e})},resetFilter:function(){var t=this;t.filter={id:void 0,sort:"",page:1,per_page:20,total_page:10},this.applyFilter()},onChangeDatetime:function(t){var e=this;e.filter[t.target.name]=moment(t.date).format("YYYY-MM-DD HH:mm")},openModal:function(t,e){this.currentGroup=e,this.currentModal=t},closeModal:function(){this.currentModal=null,this.currentGroup=null}},mounted:function(){var t=this;t.setFilterByUrl();var e=_.omit(_.pickBy(t.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page"]);this.getList(e)}});