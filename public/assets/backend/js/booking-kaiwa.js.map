{"version": 3, "sources": ["booking-kaiwa.js"], "names": [], "mappings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file": "booking-kaiwa.js", "sourcesContent": ["$.ajaxSetup({\n    headers: {\n        'X-CSRF-TOKEN': $('meta[name=\"csrf-token\"]').attr('content')\n    }\n});\n\nconst defaultCurrentObject = {\n    bookingId: undefined,\n    param: undefined,\n    user_id: undefined,\n    email: undefined,\n    skype: undefined,\n    exist: false\n};\nvar kaiwa = new Vue({\n    el: '.table-booking-kaiwa',\n    data : {\n        //manager booking\n        dateView : toDay,\n        classElementDate: 'input-datepicker',\n        classElementDateRange: 'input-daterange-datepicker',\n        dateRange: null,\n        messageCreate: null,\n        listBooking: null,\n        date: null,\n        errorMsg:   {'status': 'error', 'detail' : {'msg': 'Có lỗi xảy ra!'} },\n        waitMsg:   {'status': 'sending', 'detail' : {'msg': 'Đang xử lý!'} },\n        currentYear: null,\n        years: {},\n        months: {},\n        listDays: null,\n        dayInMonth: null,\n        // topic\n        topicName: null,\n        topics: null,\n        currentObject: {\n            bookingId: undefined,\n            param: undefined,\n            user_id: undefined,\n            email: undefined,\n            skype: undefined,\n            exist: false\n        },\n\n    },\n\n    methods : {\n        /**\n         * lấy ngày cần xem lịch\n         * @param event\n         */\n        getDate: function (event) {\n            var _this = this;\n            _this.dateView = document.getElementsByClassName(_this.classElementDate)[0].value;\n            _this.getDataByDate();\n        },\n\n        /**\n         * Tạo lịch theo một khoảng ngày\n         */\n        createListDate: function () {\n            var _this = this;\n            _this.dateRange = document.getElementsByClassName(_this.classElementDateRange)[0].value;\n            var listDate = _this.dateRange.split(' - ');\n            var url = urlCreateBooking;\n            var data = {start: listDate[0], end : listDate[1]};\n            $.ajax({\n                url: url,\n                type: 'post',\n                data: data,\n                dataType: 'json',\n                success: function (response) {\n                    _this.setMessage(response);\n                    _this.getDataByDate();\n                },\n                error: function () {\n                    _this.setMessage(_this.errorMsg);\n                }\n            });\n        },\n\n        /**\n         * Lấy dữ liệu theo một ngày\n         */\n        getDataByDate: function() {\n            var _this = this;\n            var url = urlGetList;\n            var data = {'date' : _this.dateView};\n            $.ajax({\n                url: url,\n                type: 'post',\n                data: data,\n                dataType: 'json',\n                success: function (response) {\n                    _this.listBooking = response.detail.data;\n                    _this.date = response.detail.date;\n                },\n                error: function () {\n                    _this.setMessage(_this.errorMsg);\n                }\n            });\n        },\n\n        /**\n         * Update một dữ liệu đặt lịch\n         * @param pos\n         */\n        changeData: function (pos) {\n            var _this = this;\n            var data = _this.listBooking[pos];\n            var url = urlUpdate;\n            $.ajax({\n                url: url,\n                type: 'post',\n                data: data,\n                dataType: 'json',\n                success: function (response) {\n                    _this.setMessage(response);\n                },\n                error: function () {\n                    _this.setMessage(_this.errorMsg);\n                }\n            });\n        },\n\n        /**\n         * gan message thong bao khi thay doi du lieu\n         * @param response\n         */\n        setMessage: function (response) {\n            var _this = this;\n            _this.messageCreate = response.detail.msg;\n\n            if (response.status === 'success') {\n                $.notify(_this.messageCreate, \"success\");\n            } else if (response.status === 'error') {\n                $.notify(_this.messageCreate, \"error\");\n            }\n\n        },\n\n        dateAction: function () {\n            var _this = this;\n            var date = new Date();\n            _this.currentYear = date.getFullYear();\n            _this.currentMonth = date.getMonth() + 1;\n            for (var i = 0; i < 3; i++) {\n                _this.years[i] = _this.currentYear + i;\n            }\n\n            for (var j = 1; j <=12; j++) {\n                _this.months[j] = j;\n            }\n        },\n\n        checkBooking: function () {\n            var _this = this;\n            var url = urlCheck;\n            var data = {'year': _this.currentYear, 'month': _this.currentMonth};\n            $.ajax({\n                url: url,\n                type: 'post',\n                data: data,\n                dataType: 'json',\n                success: function (response) {\n                    _this.listDays = response.detail.data;\n                }\n            });\n        },\n\n        // Topic manager\n\n        /**\n         * them moi topic\n         */\n        addTopic: function () {\n            var _this = this;\n            var url = urlAddTopic;\n            var data = {name: _this.topicName}\n            $.ajax({\n                url : url,\n                type: 'post',\n                data: data,\n                dataType: 'json',\n                success: function (response) {\n                    _this.setMessage(response);\n                    _this.topicName = null;\n                    _this.getTopicData();\n                },\n                error: function () {\n                    _this.setMessage(_this.errorMsg);\n                }\n            });\n\n        },\n        /**\n         * get data topics\n         */\n        getTopicData: function () {\n            var _this = this;\n            var url = urlGetTopic;\n            $.ajax({\n                url : url,\n                type: 'get',\n                dataType: 'json',\n                success: function (response) {\n                    _this.topics = response.detail.data;\n                },\n                error: function () {\n                    _this.setMessage(_this.errorMsg);\n                }\n            });\n        },\n\n        /**\n         * click nut edit topic name\n         * @param topic\n         */\n        editTopicName: function (topic) {\n            var elmTopicName = '.topic-name-' + topic.id;\n            var formEditTopic = '.edit-topic-name-' + topic.id;\n            $(elmTopicName).css({'display': 'none'});\n            $(formEditTopic).css({'display': 'block'});\n        },\n\n        /**\n         * update ten topic\n         * @param topic\n         * @param i\n         */\n        updateTopicName: function (topic, i) {\n            var _this = this;\n            var elmTopicName = '.topic-name-' + topic.id;\n            var formEditTopic = '.edit-topic-name-' + topic.id;\n            var data = _this.topics[i];\n            var url = updateTopic;\n            $.ajax({\n                url : url,\n                type: 'post',\n                data: data,\n                dataType: 'json',\n                success: function (response) {\n                    _this.setMessage(response);\n                    if (response.status === 'success') {\n                        $(elmTopicName).css({'display': 'block'});\n                        $(formEditTopic).css({'display': 'none'});\n                    }\n                },\n                error: function () {\n                    _this.setMessage(_this.errorMsg);\n                }\n            });\n        },\n\n        /**\n         * update status cua topic\n         * @param topic\n         * @param i\n         */\n        updateTopicStatus: function (i) {\n            var _this = this;\n            var data = _this.topics[i];\n            var url = updateTopic;\n            $.ajax({\n                url : url,\n                type: 'post',\n                data: data,\n                dataType: 'json',\n                success: function (response) {\n                    _this.getTopicData();\n                    _this.setMessage(response);\n                },\n                error: function () {\n                    _this.setMessage(_this.errorMsg);\n                }\n            });\n        },\n\n        /**\n         * xoa topic\n         * @param i\n         */\n        deleteTopic: function (i) {\n            var _this = this;\n            var data = {id: _this.topics[i].id};\n            var url = urlDeleteTopic;\n            var check = confirm('Bạn có muốn xóa chủ đề này');\n            if (check) {\n                $.ajax({\n                    url : url,\n                    type: 'post',\n                    data: data,\n                    dataType: 'json',\n                    success: function (response) {\n                        _this.getTopicData();\n                        _this.setMessage(response);\n                    },\n                    error: function () {\n                        _this.setMessage(_this.errorMsg);\n                    }\n                });\n            }\n        },\n\n        /**\n         * thoat chinh sua topic\n         * @param topic\n         * @param i\n         */\n        exitEditTopic: function (topic, i) {\n            var elmTopicName = '.topic-name-' + topic.id;\n            var formEditTopic = '.edit-topic-name-' + topic.id;\n            $(elmTopicName).css({'display': 'block'});\n            $(formEditTopic).css({'display': 'none'});\n            this.getTopicData();\n        },\n\n        getKeyPress: function (event) {\n            event.preventDefault();\n        },\n\n        processBooking: function (kaiwaId) {\n            vm = this;\n\n            var url = urlProcessBooking;\n            var data = {\n                id: kaiwaId\n            };\n            $.post(url, data, function(response){\n                if (response.code == 409) {\n                    var confirm = window.confirm(response.data.message);\n                    if (confirm) {\n                        var data2 = {\n                            id: kaiwaId,\n                            confirm: 1\n                        };\n                        $.post(url, data2, function(response){\n                            if (response.code == 200) {\n                                vm.listBooking = vm.listBooking.map(function (booking) {\n                                    if (booking.id == response.data.id) {\n                                        booking.curators = response.data.curators;\n                                    }\n                                    return booking;\n                                })\n                            }\n                        });\n                    }\n                }\n                if (response.code == 200) {\n                    vm.listBooking = vm.listBooking.map(function (booking) {\n                        if (booking.id == response.data.id) {\n                            booking.curators = response.data.curators;\n                        }\n                        return booking;\n                    })\n                }\n            });\n        },\n        // set các param xác định lớp kaiwa cần thay đổi và user cần thay đổi\n        setModalData: function (bookingId, param) {\n            vm = this;\n\n            vm.currentObject.bookingId = bookingId;\n            vm.currentObject.param = param;\n        },\n        // lấy thông tin user theo id và fill vào form\n        getUserInfo: function(event) {\n            vm = this;\n\n            var userId = event.target.value;\n            var data = {\n                user_id: userId\n            };\n            $.post(window.location.origin + '/backend/booking/kaiwa/get-user-by-id', data, function (res) {\n                if(res.code == 200) {\n                    vm.currentObject.exist = true;\n                    vm.currentObject.email = res.data.email;\n                    vm.currentObject.skype = res.data.skype;\n                }\n            });\n        },\n        // call api thêm học viên của kaiwa theo param gửi lên\n        saveBooking: function () {\n            vm = this;\n\n            if (vm.currentObject.exist) {\n                var data = _.omit(vm.currentObject, ['exist']);\n\n                $.post(window.location.origin + '/backend/booking/kaiwa/add-student', data, function (res) {\n                    if (res.code == 200) {\n                        vm.listBooking = vm.listBooking.map(function (booking) {\n                            if (booking.id == res.data.id) {\n                                booking[vm.currentObject.param] = res.data[vm.currentObject.param];\n                                booking['user_one'] = res.data['user_one'];\n                                booking['user_two'] = res.data['user_two'];\n                            }\n                            return booking;\n                        });\n                        vm.resetModalForm();\n                        $('#manual-booking').modal('hide');\n                    }\n                })\n            }\n        },\n\n        // reset form mỗi khi đóng modal\n        resetModalForm: function () {\n            vm = this;\n            vm.currentObject = {\n                bookingId: undefined,\n                param: undefined,\n                user_id: undefined,\n                email: undefined,\n                skype: undefined,\n                exist: false\n            };\n        },\n        removeStudent: function (bookingId, param) {\n            vm = this;\n\n            var confirm = window.confirm('Xác nhận xoá học viên khỏi lịch');\n            if (confirm) {\n                var data = {\n                    bookingId: bookingId,\n                    param: param\n                };\n                $.post(window.location.origin + '/backend/booking/kaiwa/remove-student', data, function (res) {\n                    if (res.code == 200) {\n                        vm.listBooking = vm.listBooking.map(function (booking) {\n                            if (booking.id == res.data.id) {\n                                booking[vm.currentObject.param] = res.data[vm.currentObject.param];\n                                booking['user_one'] = res.data['user_one'];\n                                booking['user_two'] = res.data['user_two'];\n                            }\n                            return booking;\n                        });\n                    }\n                })\n            }\n        }\n    },\n\n    created: function () {\n        this.getDataByDate();\n        this.getTopicData();\n        this.dateAction();\n    },\n});"]}