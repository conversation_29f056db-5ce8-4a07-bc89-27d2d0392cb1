<!DOCTYPE html>
<html lang="en">
  <head>

    <!-- head -->
    <meta charset="utf-8">
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Owl Carousel Documentation">
    <meta name="author" content="<PERSON>">
    <title>
      Welcome | Owl Carousel | 2.2.1
    </title>

    <!-- Stylesheets -->
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700,400italic,300italic' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="../assets/css/docs.theme.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="../assets/owlcarousel/assets/owl.theme.default.min.css">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <![endif]-->

    <!-- Favicons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="shortcut icon" href="../assets/ico/favicon.png">
    <link rel="shortcut icon" href="favicon.ico">

    <!-- Yeah i know js should not be in header. Its required for demos.-->

    <!-- javascript -->
    <script src="../assets/vendors/jquery.min.js"></script>
    <script src="../assets/owlcarousel/owl.carousel.js"></script>
  </head>
  <body>

    <!-- header -->
    <header class="header">
      <div class="row">
        <div class="large-12 columns">
          <div class="brand left">
            <h3>
              <a href="/OwlCarousel2/">owl.carousel.js</a> 
            </h3>
          </div>
          <a id="toggle-nav" class="right">
            <span></span> <span></span> <span></span> 
          </a> 
          <div class="nav-bar">
            <ul class="clearfix">
              <li> <a href="/OwlCarousel2/index.html">Home</a>  </li>
              <li> <a href="/OwlCarousel2/demos/demos.html">Demos</a>  </li>
              <li class="active">
                <a href="/OwlCarousel2/docs/started-welcome.html">Docs</a> 
              </li>
              <li>
                <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.2.1.zip">Download</a> 
                <span class="download"></span> 
              </li>
            </ul>
          </div>
        </div>
      </div>
    </header>

    <!-- title -->
    <section class="title">
      <div class="row">
        <div class="large-12 columns">
          <h1>Getting Started</h1>
        </div>
      </div>
    </section>
    <div id="docs">
      <div class="row">
        <div class="small-12 medium-3 large-3 columns">
          <ul class="side-nav">
            <li class="side-nav-head">Getting Started</li>
            <li> <a href="started-welcome.html">Welcome</a>  </li>
            <li> <a href="started-installation.html">Installation</a>  </li>
            <li> <a href="started-faq.html">FAQ</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">API</li>
            <li> <a href="api-options.html">Options</a>  </li>
            <li> <a href="api-classes.html">Classes</a>  </li>
            <li> <a href="api-events.html">Events</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">Development</li>
            <li> <a href="dev-buildin-plugins.html">Built-in Plugins</a>  </li>
            <li> <a href="dev-plugin-api.html">Plugin API</a>  </li>
            <li> <a href="dev-styles.html">Sass Styles</a>  </li>
            <li> <a href="dev-external.html">External Libs</a>  </li>
          </ul>
          <ul class="side-nav">
            <li class="side-nav-head">Support</li>
            <li> <a href="support-contributing.html">Contributing</a>  </li>
            <li> <a href="support-changelog.html">Changelog</a>  </li>
            <li> <a href="support-contact.html">Contact</a>  </li>
          </ul>
        </div>
        <div class="small-12 medium-9 large-9 columns">
          <article class="docs-content">
            <h2 id="welcome">Welcome</h2>
            <blockquote>
              <p>No matter if you are a beginner or an advanced user, starting with Owl is easy.</p>
            </blockquote>
            <h3 id="new-features">New Features</h3>
            <ul>
              <li>Infinity Loop</li>
              <li>Center item</li>
              <li>Smart Speed</li>
              <li>Stage Padding</li>
              <li>Item Margin</li>
              <li>Ability to make almost all options responsive</li>
              <li>Various Widths</li>
              <li>Callback Events</li>
              <li>RTL</li>
              <li>YouTube/Vimeo/vzaar support (fetching thumbnails as well)</li>
              <li>Anchors navigation</li>
              <li>Merged Items</li>
              <li>and more...</li>
            </ul>
            <h3 id="compatibility">Compatibility</h3>
            <p>Owl Carousel 2.x.x is not compatibile with previous version 1.x.x. The idea stays the same and it has a lot in common with Owl1 but the core code was re-written from scratch and I’m very proud with all the new features.</p>
            <p>Owl Carousel has been tested in following browsers/devices:</p>
            <ul>
              <li>Chrome</li>
              <li>Firefox</li>
              <li>Opera</li>
              <li>IE7/8/10/11</li>
              <li>iPad Safari</li>
              <li>iPod4 Safari</li>
              <li>Nexus 7 Chrome</li>
              <li>Galaxy S4</li>
              <li>Nokia 8s Windows8</li>
            </ul>
            <h3 id="library">Library</h3>
            <p>Download a version that suits your needs:</p>
            <ul>
              <li> <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/2.2.1.zip">Owl Carousel - 2.2.1</a>  - Distributed version - compiled and minified. Javascript, images and CSS included. </li>
              <li> <a href="https://github.com/OwlCarousel2/OwlCarousel2/archive/master.zip">Owl Carousel Source - 2.2.1</a>  - Source files including this documentation. All wrapped in Grunt project. </li>
            </ul>
            <h3 id="files-included">Files included</h3>
            <p>Distributed version structure:</p>
            <pre><code>owlcarousel/
├── assets/
│   ├── owl.carousel.css
│   ├── owl.carousel.min.css
│   ├── owl.theme.default.css
│   ├── owl.theme.default.min.css
│   ├── owl.theme.green.css
│   ├── owl.theme.green.min.css
│   └── owl.video.play.png
│
├── owl.carousel.js
├── owl.carousel.min.js
├── LICENSE-MIT
└── README.md</code></pre>
            <h3 id="dependecies">Dependecies</h3>
            <p>Get the latest
              <a href="https://jquery.com/">jQuery</a>  or
              <a href="http://zeptojs.com/">Zepto</a>  library. Minumum compatibile jQuery version is 1.8.3 version.</p>
            <h3 id="next-step">Next Step</h3>
            <h4 id="-installation-started-installation-html-">
              <a href="started-installation.html">Installation</a> 
            </h4>
          </article>
        </div>
      </div>
    </div>

    <!-- footer -->
    <footer class="footer">
      <div class="row">
        <div class="large-12 columns">
          <h5>
            <a href="/OwlCarousel2/docs/support-contact.html">David Deutsch</a> 
            <a id="custom-tweet-button" href="https://twitter.com/share?url=https://github.com/OwlCarousel2/OwlCarousel2&text=Owl Carousel - This is so awesome! " target="_blank"></a> 
          </h5>
        </div>
      </div>
    </footer>

    <!-- vendors -->
    <script src="../assets/vendors/highlight.js"></script>
    <script src="../assets/js/app.js"></script>
  </body>
</html>