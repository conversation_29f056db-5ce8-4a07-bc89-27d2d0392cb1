CKEDITOR.plugins.setLang("forms","ru",{button:{title:"Свойства кнопки",text:"Текст (Значение)",type:"Тип",typeBtn:"Кнопка",typeSbm:"Отправка",typeRst:"<PERSON><PERSON>ро<PERSON>"},checkboxAndRadio:{checkboxTitle:"Свойства флаговой кнопки",radioTitle:"Свойства кнопки выбора",value:"Значение",selected:"Выбрано",required:"Обязательное поле"},form:{title:"Свойства формы",menu:"Свойства формы",action:"Действие",method:"Метод",encoding:"Кодировка"},hidden:{title:"Свойства скрытого поля",name:"Имя",value:"Значение"},select:{title:"Свойства списка выбора",
selectInfo:"Информация о списке выбора",opAvail:"Доступные варианты",value:"Значение",size:"Размер",lines:"строк(и)",chkMulti:"Разрешить выбор нескольких вариантов",required:"Обязательное поле",opText:"Текст",opValue:"Значение",btnAdd:"Добавить",btnModify:"Изменить",btnUp:"Поднять",btnDown:"Опустить",btnSetValue:"Пометить как выбранное",btnDelete:"Удалить"},textarea:{title:"Свойства многострочного текстового поля",cols:"Колонок",rows:"Строк"},textfield:{title:"Свойства текстового поля",name:"Имя",
value:"Значение",charWidth:"Ширина поля (в символах)",maxChars:"Макс. количество символов",required:"Обязательное поле",type:"Тип содержимого",typeText:"Текст",typePass:"Пароль",typeEmail:"Email",typeSearch:"Поиск",typeTel:"Номер телефона",typeUrl:"Ссылка"}});