<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\ModelsFrontend\Blog;
use App\Http\ModelsFrontend\CategoryBlog;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Consts;

class BlogController extends Controller
{
    // Trang danh sách bài viết
    public function getBlogPage()
    {
        $newestBlogs = Blog::leftJoin('category_blog', 'category_blog.id', 'blog.category_id')
            ->select(['blog.*', 'category_blog.name AS category_name'])
            ->orderBy('id', 'desc')
            ->where('category_id', '<>', 0)
            ->where('category_id', '<>', Consts::FACEBOOK_CATEGORY_ID)
            ->where('public', true)
            ->take(3)
            ->get();

        $listCategories = CategoryBlog::leftJoin('blog', 'blog.category_id', 'category_blog.id')
            ->select('category_blog.*', DB::raw('COUNT(blog.id) as `count`'))
            ->where('category_blog.id', '<>', Consts::FACEBOOK_CATEGORY_ID)
            ->groupBy('category_blog.id')
            ->orderBy('sort_order', 'asc')
            ->get();

        $facebookBlog = Blog::leftJoin('category_blog', 'category_blog.id', 'blog.category_id')
            ->select(['blog.*', 'category_blog.name AS category_name'])
            ->orderBy('count_view', 'desc')
            ->where('category_id', Consts::FACEBOOK_CATEGORY_ID)
            ->where('public', true)
            ->orderBy('id', 'desc')
            ->take(6)
            ->get();

        $listBlog = Blog::leftJoin('category_blog', 'category_blog.id', 'blog.category_id')
            ->select(['blog.*', 'category_blog.name AS category_name'])
            ->orderBy('id', 'desc')
            ->where('category_id', '<>', 0)
            ->where('category_id', '<>', Consts::FACEBOOK_CATEGORY_ID)
            ->where('public', true)
            ->skip(3)
            ->take(20)
            ->get();

        return view('frontend.blog.blog')
            ->with("listCategories", $listCategories)
            ->with("facebookBlog", $facebookBlog)
            ->with("listBlog", $listBlog)
            ->with('newestBlogs', $newestBlogs);
    }

    public function loadMoreBlog(Request $request)
    {
        $listBlog = Blog::leftJoin('category_blog', 'category_blog.id', 'blog.category_id')
            ->select(['blog.*', 'category_blog.name AS category_name'])
            ->orderBy('id', 'desc');

        if ($request->categoryId) {
            $listBlog = $listBlog->where('category_id', $request->categoryId);
        } else {
            $listBlog = $listBlog->where('category_id', '<>', 0)
                ->where('category_id', '<>', Consts::FACEBOOK_CATEGORY_ID);
        }

        $listBlog = $listBlog->where('public', true)
            ->where('blog.id', '<', $request->minId)
            ->take(20)
            ->get();

        return $listBlog;
    }

    // Xem chi tiết bài viết
    public function getBlogDetail($id)
    {
        $listCategories = CategoryBlog::leftJoin('blog', 'blog.category_id', 'category_blog.id')
            ->select('category_blog.*', DB::raw('COUNT(blog.id) as `count`'))
            ->where('category_blog.id', '<>', Consts::FACEBOOK_CATEGORY_ID)
            ->groupBy('category_blog.id')
            ->orderBy('sort_order', 'asc')
            ->get();
        $listRelatedBlog = Blog::leftJoin('category_blog', 'category_blog.id', 'blog.category_id')
            ->select(['blog.*', 'category_blog.name AS category_name'])
            ->orderBy('updated_at', 'desc')
            ->where('category_id', '<>', 0)
            ->where('category_id', '<>', Consts::FACEBOOK_CATEGORY_ID)
            ->where('public', true)
            ->where('blog.id', '<>', $id)
            ->take(4)
            ->get();

        $blog = Blog::find($id);
        $blog->count_view++;
        $blog->save();

        $thisCategory = CategoryBlog::where('id', $blog->category_id)->first();

        $facebookBlog = Blog::leftJoin('category_blog', 'category_blog.id', 'blog.category_id')
            ->select(['blog.*', 'category_blog.name AS category_name'])
            ->orderBy('count_view', 'desc')
            ->where('category_id', Consts::FACEBOOK_CATEGORY_ID)
            ->where('public', true)
            ->orderBy('id', 'desc')
            ->take(6)
            ->get();

        return view('frontend.blog.detail')
            ->with("thisCategory", $thisCategory)
            ->with("listCategories", $listCategories)
            ->with("listRelatedBlog", $listRelatedBlog)
            ->with("facebookBlog", $facebookBlog)
            ->with("blog", $blog);
    }

    // Lấy ra bài viết theo category
    public function getBlogPageByCategory($categoryUrl)
    {
        $thisCategory = CategoryBlog::where('friendly_url', $categoryUrl)->first();
        if ($thisCategory != null) {
            $newestBlogs = Blog::leftJoin('category_blog', 'category_blog.id', 'blog.category_id')
                ->select(['blog.*', 'category_blog.name AS category_name'])
                ->orderBy('id', 'desc')
                ->where('category_id', $thisCategory->id)
                ->where('public', true)
                ->take(3)
                ->get();

            $listCategories = CategoryBlog::leftJoin('blog', 'blog.category_id', 'category_blog.id')
                ->select('category_blog.*', DB::raw('COUNT(blog.id) as `count`'))
                ->where('category_blog.id', '<>', Consts::FACEBOOK_CATEGORY_ID)
                ->groupBy('category_blog.id')
                ->orderBy('sort_order', 'asc')
                ->get();

            $listBlog = Blog::leftJoin('category_blog', 'category_blog.id', 'blog.category_id')
                ->select(['blog.*', 'category_blog.name AS category_name'])
                ->orderBy('id', 'desc')
                ->where('category_id', $thisCategory->id)
                ->where('public', true)
                ->skip(3)
                ->take(20)
                ->get();

            $facebookBlog = Blog::leftJoin('category_blog', 'category_blog.id', 'blog.category_id')
                ->select(['blog.*', 'category_blog.name AS category_name'])
                ->orderBy('count_view', 'desc')
                ->where('category_id', Consts::FACEBOOK_CATEGORY_ID)
                ->where('public', true)
                ->orderBy('id', 'desc')
                ->take(6)
                ->get();

            return view('frontend.blog.blog')
                ->with("thisCategory", $thisCategory)
                ->with("listCategories", $listCategories)
                ->with("listBlog", $listBlog)
                ->with("facebookBlog", $facebookBlog)
                ->with("newestBlogs", $newestBlogs);
        }

        abort(404);
    }
}
