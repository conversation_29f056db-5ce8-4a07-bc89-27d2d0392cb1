"use strict";$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),Vue.filter("dateTimeToMinute",function(t){return moment(t).format("HH:mm DD/MM/YYYY")}),Vue.component("communityGroupMembers",{template:"#groupMemberTable",props:["group"],components:{paginate:VuejsPaginate},data:function(){return{url:window.location.origin,loading:!1,items:[],filter:{id:void 0,sort:"",page:1,per_page:20,total_page:10},total_result:0,currentModal:null,modal_titles:{modalMember:"Danh sách thành viên",modalPost:"Danh sách bài đăng",modalHashtag:"Danh sách hashtag"}}},watch:{group:function(t){console.log(t)}},methods:{changePage:function(t){var e=this;e.filter.page=t;var a=_.omit(_.pickBy(e.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page"]);e.getList(a),router.replace(window.location.pathname+"?"+$.param(a))},applyFilter:function(){var t=this;t.filter.page=1;var e=_.omit(_.pickBy(t.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page","page"]);t.getList(e),router.replace(window.location.pathname+"?"+$.param(e))},onChangeCheckbox:function(t,e){vm=this,vm.filter[t]=e.target.checked?1:0,vm.applyFilter()},getList:function(t){var e=this;e.loading=!0,setTimeout(function(){$.post(window.location.origin+"/backend/community/group/".concat(e.group.id,"/user/list"),t,function(t){console.log(t),200===t.code?(e.items=t.data.users.map(function(t){return t}),e.filter.total_page=t.data.pagination.last_page,e.total_result=t.data.pagination.total):alert("Có lỗi! Liên hệ dev!!!"),e.loading=!1})},200)},setFilterByUrl:function(){var t=this,e=$.deparam.querystring();_.forEach(e,function(e,a){t.filter[a]=e==parseInt(e)?parseInt(e):e})},resetFilter:function(){var t=this;t.filter={id:void 0,sort:"",page:1,per_page:20,total_page:10},this.applyFilter()},onChangeDatetime:function(t){var e=this;e.filter[t.target.name]=moment(t.date).format("YYYY-MM-DD HH:mm")},closeModal:function(){this.currentModal=null}},mounted:function(){var t=this;t.setFilterByUrl();var e=_.omit(_.pickBy(t.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page"]);this.getList(e)}});