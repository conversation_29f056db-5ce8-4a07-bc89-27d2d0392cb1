/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'font', 'sl', {
	fontSize: {
		label: 'Velikost',
		voiceLabel: 'Velikost pisave',
		panelTitle: 'Velikost pisave'
	},
	label: '<PERSON><PERSON><PERSON>',
	panelTitle: 'Ime pisave',
	voiceLabel: '<PERSON><PERSON><PERSON>'
} );
