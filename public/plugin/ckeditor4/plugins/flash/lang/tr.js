/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'flash', 'tr', {
	access: 'Kod <PERSON>zni',
	accessAlways: '<PERSON><PERSON><PERSON>',
	accessNever: 'Asla',
	accessSameDomain: 'Aynı domain',
	alignAbsBottom: 'Tam Altı',
	alignAbsMiddle: '<PERSON> Ortası',
	alignBaseline: 'Taban Çizgis<PERSON>',
	alignTextTop: 'Yaz<PERSON> Tepeye',
	bgcolor: 'Arka Renk',
	chkFull: '<PERSON> ekrana İzinver',
	chkLoop: 'Döng<PERSON>',
	chkMenu: '<PERSON> Menüsünü Kullan',
	chkPlay: 'Otomatik Oynat',
	flashvars: 'Flash Değerleri',
	hSpace: 'Yatay <PERSON>şluk',
	properties: 'Flash Özellikleri',
	propertiesTab: 'Özellikler',
	quality: 'Kalite',
	qualityAutoHigh: 'Otomatik Yükseklik',
	qualityAutoLow: 'Otomatik Düşüklük',
	qualityBest: 'En iyi',
	qualityHigh: 'Yüksek',
	qualityLow: 'Düşük',
	qualityMedium: 'Orta',
	scale: 'Boyutlandır',
	scaleAll: 'Hepsini Göster',
	scaleFit: 'Tam Sığdır',
	scaleNoBorder: 'Kenar Yok',
	title: 'Flash Özellikleri',
	vSpace: 'Dikey Boşluk',
	validateHSpace: 'HSpace sayı olmalıdır.',
	validateSrc: 'Lütfen köprü URL\'sini yazın',
	validateVSpace: 'VSpace sayı olmalıdır.',
	windowMode: 'Pencere modu',
	windowModeOpaque: 'Opak',
	windowModeTransparent: 'Şeffaf',
	windowModeWindow: 'Pencere'
} );
