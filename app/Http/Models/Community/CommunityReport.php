<?php

namespace App\Http\Models\Community;

use App\Http\Traits\Filterable;
use App\Base\Model\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommunityReport extends BaseModel
{
    use Filterable;
    use SoftDeletes;

    protected $table = 'community_reports';

    protected $fillable = [
        'post_id',
        'comment_id',
        'user_id',
        'content_id',
        'note'
    ];
}
