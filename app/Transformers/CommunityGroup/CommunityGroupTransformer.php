<?php

namespace App\Transformers\CommunityGroup;

use League\Fractal\TransformerAbstract;
use Carbon\Carbon;

class CommunityGroupTransformer extends TransformerAbstract
{
    public function transform($groups)
    {
        return [
            'id' => $groups->id,
            'name' => $groups->name,
            'shift_type' => $groups->shift_type,
            'shift_time' => $groups->shift_time,
            'type' => $groups->type,
            'slug' => $groups->slug,
            'created_at' => Carbon::parse($groups->created_at)->toDateString(),
            'start_date' => $groups->start_date,
            'end_at' => Carbon::parse($groups->end_at)->toDateString(),
            'expired_at' => Carbon::parse($groups->expired_at)->toDateString(),
            'is_sample' => $groups->is_sample,
            'expired_after_days' => $groups->expired_after_days,
            'total_users' => $groups->users->count(),
            'total_posts' => $groups->posts->where('published_at', '<>', NULL)->count(),
            'unconfirm_posts' => $groups->posts->where('published_at', NULL)->count(),
            'avatar' => $groups->avatar,
            'banner' => $groups->banner,
            'vip_level' => $groups->vip_level,
            'status' => $groups->status,
            'size' => $groups->size,
            'group_teacher' => $groups->group_teacher,
            'vip_session' => $groups->vip_session,
            'teacher_id' => $groups->teacher ? $groups->teacher->teacher_id : null,
            'group_chat_id' => $groups->group_chat_id,
            'links' => $groups->links,
            'users' => $groups->users,
            'posts' => $groups->posts,
            'teacher' => $groups->teacher,
            'type_note' => $groups->type_note,
            'note' => $groups->note,
            'stage' => $groups->stage,
            'vip_combo' => $groups->vip_combo
        ];
    }
}
